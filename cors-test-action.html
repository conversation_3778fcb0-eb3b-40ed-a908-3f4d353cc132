<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lambda Function URL Test with Action</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <h1>Lambda Function URL Test with Action</h1>
    
    <div>
        <h2>Test Lambda Function URL with perplexityRequest Action</h2>
        <button id="testLambda">Test Lambda Function</button>
        <div id="lambdaResult"></div>
    </div>

    <script>
        document.getElementById('testLambda').addEventListener('click', async () => {
            const resultDiv = document.getElementById('lambdaResult');
            resultDiv.innerHTML = '<p>Testing Lambda function with perplexityRequest action...</p>';
            
            try {
                // Request with action parameter
                const response = await fetch('https://yqhtu7exlgaxtlrrrahaykfgnm0qqdvl.lambda-url.us-east-1.on.aws/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'perplexityRequest',
                        requestData: {
                            model: "sonar-pro",
                            messages: [
                                {
                                    role: "system",
                                    content: "You are a helpful assistant."
                                },
                                {
                                    role: "user",
                                    content: "Hello, world!"
                                }
                            ],
                            max_tokens: 100,
                            temperature: 0.1
                        }
                    })
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <p class="success">Lambda function responded with status: ${response.status}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <p class="error">Error testing Lambda function:</p>
                    <pre>${error.toString()}</pre>
                `;
            }
        });
    </script>
</body>
</html>