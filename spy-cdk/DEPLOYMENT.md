# Deployment Guide for SPY News Aggregator CDK

## Prerequisites

1. **AWS Account** with appropriate permissions
2. **AWS CLI** installed and configured with credentials
3. **Node.js 18.x or later** installed
4. **Perplexity Sonar API Key**
5. **Verified email addresses in SES** (both sender and recipient)

## Step 1: Configure AWS CLI

If you haven't already configured the AWS CLI:

```bash
aws configure
```

Enter your:
- AWS Access Key ID
- AWS Secret Access Key
- Default region (recommend us-east-1)
- Default output format (json)

## Step 2: Update Email Addresses

1. Modify the email addresses in `lib/spy-news-stack.ts`:

```typescript
// Replace <EMAIL> with your actual email
marketPredictionTopic.addSubscription(
  new subscriptions.EmailSubscription('<EMAIL>')
);
```

2. Also update the Lambda environment variables:

```typescript
environment: {
  SNS_TOPIC_ARN: marketPredictionTopic.topicArn,
  RECIPIENT_EMAIL: '<EMAIL>', // Replace
  SENDER_EMAIL: '<EMAIL>', // Replace with verified SES email
},
```

## Step 3: Verify Email Addresses in SES

1. Go to AWS Console > SES > Email Addresses
2. Click "Verify a New Email Address"
3. Enter both sender and recipient email addresses
4. Check your inbox for verification emails and confirm them

Note: If your account is in SES sandbox mode, both sender and recipient must be verified.

## Step 4: Create a Systems Manager Parameter for the Sonar API Key

For secure API key storage:

```bash
aws ssm put-parameter \
    --name "/spy-news/sonar-api-key" \
    --value "your-actual-sonar-api-key" \
    --type "SecureString" \
    --description "Perplexity Sonar API Key"
```

Then update the Lambda environment in `lib/spy-news-stack.ts`:

```typescript
environment: {
  SNS_TOPIC_ARN: marketPredictionTopic.topicArn,
  RECIPIENT_EMAIL: '<EMAIL>',
  SENDER_EMAIL: '<EMAIL>',
  SONAR_API_KEY_PARAM: '/spy-news/sonar-api-key', // Parameter name
},
```

And update the Lambda code in `lambda-assets/index.ts` to fetch the parameter:

```typescript
import { SSMClient, GetParameterCommand } from '@aws-sdk/client-ssm';

// Create SSM client
const ssmClient = new SSMClient({ region: process.env.AWS_REGION });

// Function to get Sonar API key from SSM
async function getSonarApiKey() {
  const paramName = process.env.SONAR_API_KEY_PARAM || '';
  try {
    const response = await ssmClient.send(
      new GetParameterCommand({
        Name: paramName,
        WithDecryption: true,
      })
    );
    return response.Parameter?.Value || '';
  } catch (error) {
    console.error('Error retrieving Sonar API key:', error);
    throw error;
  }
}

// Update handler to fetch the key
export const handler = async (event: any): Promise<any> => {
  try {
    console.log('Executing SPY News Lambda function');
    
    // Get the Sonar API key from SSM
    const SONAR_API_KEY = await getSonarApiKey();
    
    // Rest of the function...
  }
  // ...
}
```

## Step 5: Install Dependencies and Build

In the spy-cdk directory:

```bash
# Install dependencies
npm install

# Build the project
npm run build

# Build Lambda assets
cd lambda-assets
npm install
npm run build
cd ..
```

## Step 6: Bootstrap the CDK Environment

If you haven't already bootstrapped your AWS environment for CDK:

```bash
npx cdk bootstrap
```

## Step 7: Review the CDK Diff

Preview the changes that will be made:

```bash
npx cdk diff
```

## Step 8: Deploy the Stack

Deploy the CDK stack:

```bash
npx cdk deploy
```

When prompted to approve security-related IAM changes, enter 'y'.

## Step 9: Verify Deployment

1. Check the AWS Console for the created resources:
   - CloudFormation stack
   - Lambda function
   - EventBridge rule
   - SNS topic
   - SES configuration

2. You can manually test the Lambda function from the Lambda console.

## Step 10: Update Lambda Function Code (If Needed)

If you make changes to the Lambda code:

```bash
# From the spy-cdk directory
cd lambda-assets
npm run build
cd ..
npm run build
npx cdk deploy
```

## Troubleshooting

1. **Lambda Errors**: Check CloudWatch Logs for Lambda error messages.

2. **Email Delivery Issues**:
   - Verify both sender and recipient emails in SES
   - Check if your account is still in SES sandbox mode
   - If in sandbox mode, submit a request to move out of it

3. **API Key Issues**:
   - Verify the SSM parameter was created correctly
   - Check that the Lambda has permissions to access the parameter

4. **Deployment Failures**:
   - Check CloudFormation events for detailed error messages
   - Ensure your IAM user has sufficient permissions

## Clean Up

To remove all deployed resources:

```bash
npx cdk destroy
```