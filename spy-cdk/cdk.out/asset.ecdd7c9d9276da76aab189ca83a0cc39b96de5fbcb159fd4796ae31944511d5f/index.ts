import { SNSClient, PublishCommand } from '@aws-sdk/client-sns';
import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses';
import { SSMClient, GetParameterCommand } from '@aws-sdk/client-ssm';
import axios from 'axios';

// Configuration
const SNS_TOPIC_ARN = process.env.SNS_TOPIC_ARN || '';
const RECIPIENT_EMAIL = process.env.RECIPIENT_EMAIL || '';
const SENDER_EMAIL = process.env.SENDER_EMAIL || '';
const SONAR_API_KEY_PARAM = process.env.SONAR_API_KEY_PARAM || '';

// Create clients
const snsClient = new SNSClient({ region: process.env.AWS_REGION });
const sesClient = new SESClient({ region: process.env.AWS_REGION });
const ssmClient = new SSMClient({ region: process.env.AWS_REGION });

// Function to get Sonar API key from SSM Parameter Store
async function getSonarApiKey(): Promise<string> {
  try {
    const response = await ssmClient.send(
      new GetParameterCommand({
        Name: SONAR_API_KEY_PARAM,
        WithDecryption: true,
      })
    );
    
    if (!response.Parameter?.Value) {
      throw new Error(`Parameter ${SONAR_API_KEY_PARAM} not found or has no value`);
    }
    
    return response.Parameter.Value;
  } catch (error) {
    console.error('Error retrieving Sonar API key from SSM:', error);
    throw error;
  }
}

// Function to fetch news and market data from external API
async function fetchNewsAndMarketData() {
  // This would be replaced with your actual implementation to fetch news data
  // For now, we'll just return some placeholder data
  return {
    news: [
      { title: 'Fed raises interest rates', source: 'Financial Times', url: 'https://example.com/article1' },
      { title: 'Tech stocks rally on earnings beats', source: 'CNBC', url: 'https://example.com/article2' },
      { title: 'Inflation concerns grow', source: 'Wall Street Journal', url: 'https://example.com/article3' },
    ],
    marketData: {
      previousClose: 450.32,
      volume: 120000000,
      avgVolume: 100000000,
    }
  };
}

// Function to call Perplexity Sonar API
async function callSonarAPI(newsContext: string) {
  try {
    // Get the API key from SSM Parameter Store
    const apiKey = await getSonarApiKey();
    
    const response = await axios.post('https://api.perplexity.ai/chat/completions', {
      model: 'sonar-pro',
      messages: [
        {
          role: 'system',
          content: 'You are a financial analyst assistant. Analyze news articles and predict SPY market movement.'
        },
        {
          role: 'user',
          content: `Based on the following news articles, predict whether SPY (S&P 500 ETF) will open higher or lower today and explain why. Include specific percentage predictions if possible. News context: ${newsContext}`
        }
      ],
      temperature: 0.1,
      max_tokens: 4000,
      web_search_options: { "search_context_size": "high"}
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.data && response.data.choices && response.data.choices.length > 0) {
      return response.data.choices[0].message.content;
    } else {
      throw new Error('Unexpected response structure from Sonar API');
    }
  } catch (error: any) {
    console.error('Error calling Sonar API:', error.message);
    throw error;
  }
}

// Function to generate an options strategy
async function generateOptionsStrategy(marketPrediction: string, newsContext: string) {
  try {
    // Get the API key from SSM Parameter Store
    const apiKey = await getSonarApiKey();
    
    const response = await axios.post('https://api.perplexity.ai/chat/completions', {
      model: 'sonar-pro',
      messages: [
        {
          role: 'system',
          content: 'You are a financial options trading strategy expert.'
        },
        {
          role: 'user',
          content: `Based on the following market prediction and news items, generate an options trading strategy for SPY. Include specific strike prices, expiration dates, and detailed profit/loss scenarios. Format your output as JSON with a description field and a structured strategy object.

Market Prediction:
${marketPrediction}

News Context:
${newsContext}

Return the response in the following JSON structure:
{
  "marketOutlook": "bullish/bearish/neutral summary",
  "recommendation": "brief strategy name",
  "strategy": {
    "type": "spread/condor/straddle/etc",
    "direction": "bullish/bearish/neutral",
    "legs": [
      {
        "action": "buy/sell",
        "optionType": "call/put",
        "strike": 450,
        "expiration": "YYYY-MM-DD",
        "contracts": 1,
        "premium": 2.45
      }
    ],
    "maxProfit": 245,
    "maxLoss": 755,
    "breakEven": [448.55]
  },
  "relatedStocks": [
    {
      "ticker": "AAPL",
      "recommendation": "buy/hold/sell",
      "reason": "brief explanation"
    }
  ],
  "analysis": "detailed explanation of why this strategy makes sense given the market conditions"
}`
        }
      ],
      temperature: 0.1,
      max_tokens: 4000,
      web_search_options: { "search_context_size": "high" }
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.data && response.data.choices && response.data.choices.length > 0) {
      return response.data.choices[0].message.content;
    } else {
      throw new Error('Unexpected response structure from Sonar API');
    }
  } catch (error: any) {
    console.error('Error generating options strategy:', error.message);
    throw error;
  }
}

// Format data for email
function formatEmailBody(marketPrediction: string, optionsStrategy: string) {
  let formattedStrategy: string;
  
  try {
    // Try to parse the strategy as JSON for better formatting
    const strategyJson = JSON.parse(optionsStrategy);
    formattedStrategy = `
<h2>Market Outlook: ${strategyJson.marketOutlook}</h2>
<h3>Recommended Strategy: ${strategyJson.recommendation}</h3>

<h4>Strategy Details:</h4>
<ul>
  <li>Type: ${strategyJson.strategy.type}</li>
  <li>Direction: ${strategyJson.strategy.direction}</li>
  <li>Max Profit: $${strategyJson.strategy.maxProfit}</li>
  <li>Max Loss: $${strategyJson.strategy.maxLoss}</li>
  <li>Break Even: ${strategyJson.strategy.breakEven.join(', ')}</li>
</ul>

<h4>Option Legs:</h4>
<table border="1" cellpadding="5">
  <tr>
    <th>Action</th>
    <th>Type</th>
    <th>Strike</th>
    <th>Expiration</th>
    <th>Contracts</th>
    <th>Premium</th>
  </tr>
  ${strategyJson.strategy.legs.map((leg: any) => `
  <tr>
    <td>${leg.action}</td>
    <td>${leg.optionType}</td>
    <td>$${leg.strike}</td>
    <td>${leg.expiration}</td>
    <td>${leg.contracts}</td>
    <td>$${leg.premium}</td>
  </tr>
  `).join('')}
</table>

<h4>Related Stocks:</h4>
<table border="1" cellpadding="5">
  <tr>
    <th>Ticker</th>
    <th>Recommendation</th>
    <th>Reason</th>
  </tr>
  ${strategyJson.relatedStocks.map((stock: any) => `
  <tr>
    <td>${stock.ticker}</td>
    <td>${stock.recommendation}</td>
    <td>${stock.reason}</td>
  </tr>
  `).join('')}
</table>

<h4>Analysis:</h4>
<p>${strategyJson.analysis}</p>
    `;
  } catch (e) {
    // If JSON parsing fails, just use the raw strategy text
    formattedStrategy = `<pre>${optionsStrategy}</pre>`;
  }

  return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>SPY Market Prediction for ${new Date().toLocaleDateString()}</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; }
    h1 { color: #0066cc; border-bottom: 2px solid #eee; padding-bottom: 10px; }
    h2 { color: #0099cc; margin-top: 30px; }
    h3 { color: #00aa99; }
    .prediction { padding: 15px; background-color: #f9f9f9; border-left: 4px solid #0066cc; margin: 20px 0; }
    .strategy { padding: 15px; background-color: #f5f5f5; border-left: 4px solid #00aa99; margin: 20px 0; }
    table { border-collapse: collapse; width: 100%; margin: 15px 0; }
    th { background-color: #f0f0f0; }
    .disclaimer { font-size: 12px; color: #999; margin-top: 30px; border-top: 1px solid #eee; padding-top: 10px; }
  </style>
</head>
<body>
  <h1>SPY Market Prediction for ${new Date().toLocaleDateString()}</h1>
  
  <h2>Market Prediction</h2>
  <div class="prediction">
    ${marketPrediction}
  </div>
  
  <h2>Options Strategy</h2>
  <div class="strategy">
    ${formattedStrategy}
  </div>
  
  <div class="disclaimer">
    <p><strong>Disclaimer:</strong> This email contains AI-generated market predictions and options strategies. The information provided should not be considered as financial advice. Always conduct your own research and consult with a licensed financial advisor before making investment decisions.</p>
    <p>This automated analysis is provided by SPY News Aggregator.</p>
  </div>
</body>
</html>
  `;
}

// Main handler function
export const handler = async (event: any): Promise<any> => {
  try {
    console.log('Executing SPY News Lambda function');
    
    // 1. Fetch news and market data
    const { news, marketData } = await fetchNewsAndMarketData();
    
    // Prepare the news context for the API
    const newsContext = news.map(item => `${item.title} (${item.source}): ${item.url}`).join('\n');
    
    // 2. Call Sonar API to get market prediction
    console.log('Calling Sonar API to generate market prediction...');
    const marketPrediction = await callSonarAPI(newsContext);
    console.log('Market prediction generated successfully');
    
    // 3. Generate options strategy
    console.log('Generating options strategy...');
    const optionsStrategy = await generateOptionsStrategy(marketPrediction, newsContext);
    console.log('Options strategy generated successfully');
    
    // 4. Publish to SNS
    await snsClient.send(new PublishCommand({
      TopicArn: SNS_TOPIC_ARN,
      Subject: `SPY Market Prediction for ${new Date().toLocaleDateString()}`,
      Message: `Market Prediction:\n${marketPrediction}\n\nOptions Strategy:\n${optionsStrategy}`,
    }));
    console.log('Published to SNS');
    
    // 5. Send an email via SES
    if (RECIPIENT_EMAIL && SENDER_EMAIL) {
      const emailParams = {
        Destination: {
          ToAddresses: [RECIPIENT_EMAIL],
        },
        Message: {
          Body: {
            Html: {
              Charset: 'UTF-8',
              Data: formatEmailBody(marketPrediction, optionsStrategy),
            },
          },
          Subject: {
            Charset: 'UTF-8',
            Data: `SPY Market Prediction for ${new Date().toLocaleDateString()}`,
          },
        },
        Source: SENDER_EMAIL,
      };
      
      await sesClient.send(new SendEmailCommand(emailParams));
      console.log('Email sent via SES');
    }
    
    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'SPY news analysis completed successfully',
        marketPrediction: marketPrediction.substring(0, 100) + '...',
        timestamp: new Date().toISOString(),
      }),
    };
  } catch (error: any) {
    console.error('Error in SPY News Lambda:', error);
    
    // Notify about the error
    try {
      await snsClient.send(new PublishCommand({
        TopicArn: SNS_TOPIC_ARN,
        Subject: 'Error in SPY News Analysis',
        Message: `An error occurred while running the SPY news analysis: ${error.message}`,
      }));
    } catch (snsError) {
      console.error('Failed to publish error to SNS:', snsError);
    }
    
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: 'Error processing SPY news analysis',
        error: error.message,
        timestamp: new Date().toISOString(),
      }),
    };
  }
};