{"version": "tree-0.1", "tree": {"id": "App", "path": "", "children": {"SpyNewsStack": {"id": "SpyNewsStack", "path": "SpyNewsStack", "children": {"MarketPredictionTopic": {"id": "MarketPredictionTopic", "path": "SpyNewsStack/MarketPredictionTopic", "children": {"Resource": {"id": "Resource", "path": "SpyNewsStack/MarketPredictionTopic/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::SNS::Topic", "aws:cdk:cloudformation:props": {"displayName": "SPY Market Predictions", "topicName": "spy-news-market-prediction"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_sns.CfnTopic", "version": "2.187.0"}}, "<EMAIL>": {"id": "<EMAIL>", "path": "SpyNewsStack/MarketPredictionTopic/<EMAIL>", "children": {"Resource": {"id": "Resource", "path": "SpyNewsStack/MarketPredictionTopic/<EMAIL>/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::SNS::Subscription", "aws:cdk:cloudformation:props": {"endpoint": "<EMAIL>", "protocol": "email", "topicArn": {"Ref": "MarketPredictionTopicBD8AA2B7"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_sns.CfnSubscription", "version": "2.187.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_sns.Subscription", "version": "2.187.0", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_sns.Topic", "version": "2.187.0", "metadata": []}}, "SpyNewsLambdaRole": {"id": "SpyNewsLambdaRole", "path": "SpyNewsStack/SpyNewsLambdaRole", "children": {"ImportSpyNewsLambdaRole": {"id": "ImportSpyNewsLambdaRole", "path": "SpyNewsStack/SpyNewsLambdaRole/ImportSpyNewsLambdaRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.187.0", "metadata": []}}, "Resource": {"id": "Resource", "path": "SpyNewsStack/SpyNewsLambdaRole/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "description": "Role for the SPY News Lambda function", "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.187.0"}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "SpyNewsStack/SpyNewsLambdaRole/DefaultPolicy", "children": {"Resource": {"id": "Resource", "path": "SpyNewsStack/SpyNewsLambdaRole/DefaultPolicy/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": ["ses:SendEmail", "ses:SendRawEmail", "sns:Publish", "ssm:GetParameter", "ssm:GetParameters"], "Effect": "Allow", "Resource": "*"}, {"Action": "sns:Publish", "Effect": "Allow", "Resource": {"Ref": "MarketPredictionTopicBD8AA2B7"}}], "Version": "2012-10-17"}, "policyName": "SpyNewsLambdaRoleDefaultPolicy03512C4A", "roles": [{"Ref": "SpyNewsLambdaRole6BC47458"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.187.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.187.0", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.187.0", "metadata": []}}, "SpyNewsLambdaFunction": {"id": "SpyNewsLambdaFunction", "path": "SpyNewsStack/SpyNewsLambdaFunction", "children": {"Code": {"id": "Code", "path": "SpyNewsStack/SpyNewsLambdaFunction/Code", "children": {"Stage": {"id": "Stage", "path": "SpyNewsStack/SpyNewsLambdaFunction/Code/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.187.0"}}, "AssetBucket": {"id": "AssetBucket", "path": "SpyNewsStack/SpyNewsLambdaFunction/Code/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.187.0", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.187.0"}}, "Resource": {"id": "Resource", "path": "SpyNewsStack/SpyNewsLambdaFunction/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Function", "aws:cdk:cloudformation:props": {"code": {"s3Bucket": "cdk-hnb659fds-assets-482625028438-us-east-1", "s3Key": "3be21eddeba5a09a89663e8ed074cf36ab75559b41d2b7d7a2cb44e9ddf02ead.zip"}, "environment": {"variables": {"SNS_TOPIC_ARN": {"Ref": "MarketPredictionTopicBD8AA2B7"}, "RECIPIENT_EMAILS": "<EMAIL>, <EMAIL>", "SENDER_EMAIL": "<EMAIL>", "SONAR_API_KEY_PARAM": "/spy-news/sonar-api-key", "FINNHUB_API_KEY_PARAM": "/spy-news/finnhub-api-key"}}, "handler": "index.handler", "memorySize": 512, "role": {"Fn::GetAtt": ["SpyNewsLambdaRole6BC47458", "<PERSON><PERSON>"]}, "runtime": "nodejs18.x", "timeout": 600}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnFunction", "version": "2.187.0"}}, "FunctionUrl": {"id": "FunctionUrl", "path": "SpyNewsStack/SpyNewsLambdaFunction/FunctionUrl", "children": {"Resource": {"id": "Resource", "path": "SpyNewsStack/SpyNewsLambdaFunction/FunctionUrl/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Url", "aws:cdk:cloudformation:props": {"authType": "NONE", "cors": {"allowCredentials": false, "allowHeaders": ["Content-Type", "Authorization"], "allowMethods": ["GET", "POST", "HEAD", "DELETE", "PUT", "PATCH"], "allowOrigins": ["*"]}, "targetFunctionArn": {"Fn::GetAtt": ["SpyNewsLambdaFunctionAAE3CD75", "<PERSON><PERSON>"]}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnUrl", "version": "2.187.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.FunctionUrl", "version": "2.187.0", "metadata": []}}, "invoke-function-url": {"id": "invoke-function-url", "path": "SpyNewsStack/SpyNewsLambdaFunction/invoke-function-url", "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunctionUrl", "functionName": {"Fn::GetAtt": ["SpyNewsLambdaFunctionAAE3CD75", "<PERSON><PERSON>"]}, "functionUrlAuthType": "NONE", "principal": "*"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.187.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.Function", "version": "2.187.0", "metadata": []}}, "LambdaFunctionUrl": {"id": "LambdaFunctionUrl", "path": "SpyNewsStack/LambdaFunctionUrl", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.187.0"}}, "ScheduleRule": {"id": "ScheduleRule", "path": "SpyNewsStack/ScheduleRule", "children": {"Resource": {"id": "Resource", "path": "SpyNewsStack/ScheduleRule/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Events::Rule", "aws:cdk:cloudformation:props": {"description": "Run SPY News analysis at 9:00 AM ET on weekdays (trading days)", "scheduleExpression": "cron(0 13 ? * MON-FRI *)", "state": "ENABLED", "targets": [{"id": "Target0", "arn": {"Fn::GetAtt": ["SpyNewsLambdaFunctionAAE3CD75", "<PERSON><PERSON>"]}}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_events.CfnRule", "version": "2.187.0"}}, "AllowEventRuleSpyNewsStackSpyNewsLambdaFunction57319F6C": {"id": "AllowEventRuleSpyNewsStackSpyNewsLambdaFunction57319F6C", "path": "SpyNewsStack/ScheduleRule/AllowEventRuleSpyNewsStackSpyNewsLambdaFunction57319F6C", "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["SpyNewsLambdaFunctionAAE3CD75", "<PERSON><PERSON>"]}, "principal": "events.amazonaws.com", "sourceArn": {"Fn::GetAtt": ["ScheduleRuleDA5BD877", "<PERSON><PERSON>"]}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.187.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_events.Rule", "version": "2.187.0", "metadata": []}}, "MarketPredictionTopicARN": {"id": "MarketPredictionTopicARN", "path": "SpyNewsStack/MarketPredictionTopicARN", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.187.0"}}, "CDKMetadata": {"id": "CDKMetadata", "path": "SpyNewsStack/CDKMetadata", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "SpyNewsStack/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.187.0"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "BootstrapVersion": {"id": "BootstrapVersion", "path": "SpyNewsStack/BootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.187.0"}}, "CheckBootstrapVersion": {"id": "CheckBootstrapVersion", "path": "SpyNewsStack/CheckBootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnRule", "version": "2.187.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.<PERSON><PERSON>", "version": "2.187.0"}}, "Tree": {"id": "Tree", "path": "Tree", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}}, "constructInfo": {"fqn": "aws-cdk-lib.App", "version": "2.187.0"}}}