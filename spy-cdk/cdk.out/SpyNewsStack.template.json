{"Resources": {"MarketPredictionTopicBD8AA2B7": {"Type": "AWS::SNS::Topic", "Properties": {"DisplayName": "SPY Market Predictions", "TopicName": "spy-news-market-prediction"}, "Metadata": {"aws:cdk:path": "SpyNewsStack/MarketPredictionTopic/Resource"}}, "MarketPredictionTopicmneto6gatecheduADA94466": {"Type": "AWS::SNS::Subscription", "Properties": {"Endpoint": "<EMAIL>", "Protocol": "email", "TopicArn": {"Ref": "MarketPredictionTopicBD8AA2B7"}}, "Metadata": {"aws:cdk:path": "SpyNewsStack/MarketPredictionTopic/<EMAIL>/Resource"}}, "SpyNewsLambdaRole6BC47458": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "Description": "Role for the SPY News Lambda function", "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}, "Metadata": {"aws:cdk:path": "SpyNewsStack/SpyNewsLambdaRole/Resource"}}, "SpyNewsLambdaRoleDefaultPolicy03512C4A": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["ses:SendEmail", "ses:SendRawEmail", "sns:Publish", "ssm:GetParameter", "ssm:GetParameters"], "Effect": "Allow", "Resource": "*"}, {"Action": "sns:Publish", "Effect": "Allow", "Resource": {"Ref": "MarketPredictionTopicBD8AA2B7"}}], "Version": "2012-10-17"}, "PolicyName": "SpyNewsLambdaRoleDefaultPolicy03512C4A", "Roles": [{"Ref": "SpyNewsLambdaRole6BC47458"}]}, "Metadata": {"aws:cdk:path": "SpyNewsStack/SpyNewsLambdaRole/DefaultPolicy/Resource"}}, "SpyNewsLambdaFunctionAAE3CD75": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": "cdk-hnb659fds-assets-482625028438-us-east-1", "S3Key": "3be21eddeba5a09a89663e8ed074cf36ab75559b41d2b7d7a2cb44e9ddf02ead.zip"}, "Environment": {"Variables": {"SNS_TOPIC_ARN": {"Ref": "MarketPredictionTopicBD8AA2B7"}, "RECIPIENT_EMAILS": "<EMAIL>, <EMAIL>", "SENDER_EMAIL": "<EMAIL>", "SONAR_API_KEY_PARAM": "/spy-news/sonar-api-key", "FINNHUB_API_KEY_PARAM": "/spy-news/finnhub-api-key"}}, "Handler": "index.handler", "MemorySize": 512, "Role": {"Fn::GetAtt": ["SpyNewsLambdaRole6BC47458", "<PERSON><PERSON>"]}, "Runtime": "nodejs18.x", "Timeout": 600}, "DependsOn": ["SpyNewsLambdaRoleDefaultPolicy03512C4A", "SpyNewsLambdaRole6BC47458"], "Metadata": {"aws:cdk:path": "SpyNewsStack/SpyNewsLambdaFunction/Resource", "aws:asset:path": "asset.3be21eddeba5a09a89663e8ed074cf36ab75559b41d2b7d7a2cb44e9ddf02ead.zip", "aws:asset:is-bundled": false, "aws:asset:property": "Code"}}, "SpyNewsLambdaFunctionFunctionUrl45CA6F56": {"Type": "AWS::Lambda::Url", "Properties": {"AuthType": "NONE", "Cors": {"AllowCredentials": false, "AllowHeaders": ["Content-Type", "Authorization"], "AllowMethods": ["GET", "POST", "HEAD", "DELETE", "PUT", "PATCH"], "AllowOrigins": ["*"]}, "TargetFunctionArn": {"Fn::GetAtt": ["SpyNewsLambdaFunctionAAE3CD75", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "SpyNewsStack/SpyNewsLambdaFunction/FunctionUrl/Resource"}}, "SpyNewsLambdaFunctioninvokefunctionurl19C6EC7D": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunctionUrl", "FunctionName": {"Fn::GetAtt": ["SpyNewsLambdaFunctionAAE3CD75", "<PERSON><PERSON>"]}, "FunctionUrlAuthType": "NONE", "Principal": "*"}, "Metadata": {"aws:cdk:path": "SpyNewsStack/SpyNewsLambdaFunction/invoke-function-url"}}, "ScheduleRuleDA5BD877": {"Type": "AWS::Events::Rule", "Properties": {"Description": "Run SPY News analysis at 9:00 AM ET on weekdays (trading days)", "ScheduleExpression": "cron(0 13 ? * MON-FRI *)", "State": "ENABLED", "Targets": [{"Arn": {"Fn::GetAtt": ["SpyNewsLambdaFunctionAAE3CD75", "<PERSON><PERSON>"]}, "Id": "Target0"}]}, "Metadata": {"aws:cdk:path": "SpyNewsStack/ScheduleRule/Resource"}}, "ScheduleRuleAllowEventRuleSpyNewsStackSpyNewsLambdaFunction57319F6CB75C3916": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["SpyNewsLambdaFunctionAAE3CD75", "<PERSON><PERSON>"]}, "Principal": "events.amazonaws.com", "SourceArn": {"Fn::GetAtt": ["ScheduleRuleDA5BD877", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "SpyNewsStack/ScheduleRule/AllowEventRuleSpyNewsStackSpyNewsLambdaFunction57319F6C"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/02Qy27CMBBFv6V7Z9rAgm4BqVtQgDVynAENcezIY4Mqy/9eOwHU1bkP+TGzgPp7BV8f8sGV6vpKUwvx4KXqRY7OkQ1DPNqRlNhezCwOoWXlaPRkTUn/+yRIDhAbq7FUE/dWk/otdlZJaDm0nYT4E4x63fLWL3FyuuRP7NENxDw9wcuzZEbPsC7IHjZB9eg3klHgHU2uYhOef8hMSTTINjiFYjqTR7ySuZZ+F/wYfBLGdgg3/rwvllDXeSc3JqpcMJ4GhGbmH3BjUlMwAQAA"}, "Metadata": {"aws:cdk:path": "SpyNewsStack/CDKMetadata/Default"}}}, "Outputs": {"LambdaFunctionUrl": {"Description": "URL of the Lambda function", "Value": {"Fn::GetAtt": ["SpyNewsLambdaFunctionFunctionUrl45CA6F56", "FunctionUrl"]}, "Export": {"Name": "SpyNewsLambdaUrl"}}, "MarketPredictionTopicARN": {"Description": "ARN of the Market Prediction SNS topic", "Value": {"Ref": "MarketPredictionTopicBD8AA2B7"}, "Export": {"Name": "SpyNewsTopicARN"}}}, "Parameters": {"BootstrapVersion": {"Type": "AWS::SSM::Parameter::Value<String>", "Default": "/cdk-bootstrap/hnb659fds/version", "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]"}}, "Rules": {"CheckBootstrapVersion": {"Assertions": [{"Assert": {"Fn::Not": [{"Fn::Contains": [["1", "2", "3", "4", "5"], {"Ref": "BootstrapVersion"}]}]}, "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI."}]}}}