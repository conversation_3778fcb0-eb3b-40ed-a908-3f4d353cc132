{"version": "41.0.0", "files": {"3be21eddeba5a09a89663e8ed074cf36ab75559b41d2b7d7a2cb44e9ddf02ead": {"displayName": "SpyNewsLambdaFunction/Code", "source": {"path": "asset.3be21eddeba5a09a89663e8ed074cf36ab75559b41d2b7d7a2cb44e9ddf02ead.zip", "packaging": "file"}, "destinations": {"482625028438-us-east-1": {"bucketName": "cdk-hnb659fds-assets-482625028438-us-east-1", "objectKey": "3be21eddeba5a09a89663e8ed074cf36ab75559b41d2b7d7a2cb44e9ddf02ead.zip", "region": "us-east-1", "assumeRoleArn": "arn:${AWS::Partition}:iam::482625028438:role/cdk-hnb659fds-file-publishing-role-482625028438-us-east-1"}}}, "9b13bd8ebcc62597420511629c7906d25da17a27f63af600e505a69a2955c59b": {"displayName": "SpyNewsStack Template", "source": {"path": "SpyNewsStack.template.json", "packaging": "file"}, "destinations": {"482625028438-us-east-1": {"bucketName": "cdk-hnb659fds-assets-482625028438-us-east-1", "objectKey": "9b13bd8ebcc62597420511629c7906d25da17a27f63af600e505a69a2955c59b.json", "region": "us-east-1", "assumeRoleArn": "arn:${AWS::Partition}:iam::482625028438:role/cdk-hnb659fds-file-publishing-role-482625028438-us-east-1"}}}}, "dockerImages": {}}