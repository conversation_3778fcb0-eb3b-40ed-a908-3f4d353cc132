{"version": "41.0.0", "artifacts": {"SpyNewsStack.assets": {"type": "cdk:asset-manifest", "properties": {"file": "SpyNewsStack.assets.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "SpyNewsStack": {"type": "aws:cloudformation:stack", "environment": "aws://482625028438/us-east-1", "properties": {"templateFile": "SpyNewsStack.template.json", "terminationProtection": false, "tags": {"application": "spy-news-aggregator", "environment": "production"}, "validateOnSynth": false, "assumeRoleArn": "arn:${AWS::Partition}:iam::482625028438:role/cdk-hnb659fds-deploy-role-482625028438-us-east-1", "cloudFormationExecutionRoleArn": "arn:${AWS::Partition}:iam::482625028438:role/cdk-hnb659fds-cfn-exec-role-482625028438-us-east-1", "stackTemplateAssetObjectUrl": "s3://cdk-hnb659fds-assets-482625028438-us-east-1/9b13bd8ebcc62597420511629c7906d25da17a27f63af600e505a69a2955c59b.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version", "additionalDependencies": ["SpyNewsStack.assets"], "lookupRole": {"arn": "arn:${AWS::Partition}:iam::482625028438:role/cdk-hnb659fds-lookup-role-482625028438-us-east-1", "requiresBootstrapStackVersion": 8, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "dependencies": ["SpyNewsStack.assets"], "metadata": {"/SpyNewsStack": [{"type": "aws:cdk:stack-tags", "data": [{"Key": "application", "Value": "spy-news-aggregator"}, {"Key": "environment", "Value": "production"}]}], "/SpyNewsStack/MarketPredictionTopic/Resource": [{"type": "aws:cdk:logicalId", "data": "MarketPredictionTopicBD8AA2B7"}], "/SpyNewsStack/MarketPredictionTopic/<EMAIL>/Resource": [{"type": "aws:cdk:logicalId", "data": "MarketPredictionTopicmneto6gatecheduADA94466"}], "/SpyNewsStack/SpyNewsLambdaRole/Resource": [{"type": "aws:cdk:logicalId", "data": "SpyNewsLambdaRole6BC47458"}], "/SpyNewsStack/SpyNewsLambdaRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "SpyNewsLambdaRoleDefaultPolicy03512C4A"}], "/SpyNewsStack/SpyNewsLambdaFunction/Resource": [{"type": "aws:cdk:logicalId", "data": "SpyNewsLambdaFunctionAAE3CD75"}], "/SpyNewsStack/SpyNewsLambdaFunction/FunctionUrl/Resource": [{"type": "aws:cdk:logicalId", "data": "SpyNewsLambdaFunctionFunctionUrl45CA6F56"}], "/SpyNewsStack/SpyNewsLambdaFunction/invoke-function-url": [{"type": "aws:cdk:logicalId", "data": "SpyNewsLambdaFunctioninvokefunctionurl19C6EC7D"}], "/SpyNewsStack/LambdaFunctionUrl": [{"type": "aws:cdk:logicalId", "data": "LambdaFunctionUrl"}], "/SpyNewsStack/ScheduleRule/Resource": [{"type": "aws:cdk:logicalId", "data": "ScheduleRuleDA5BD877"}], "/SpyNewsStack/ScheduleRule/AllowEventRuleSpyNewsStackSpyNewsLambdaFunction57319F6C": [{"type": "aws:cdk:logicalId", "data": "ScheduleRuleAllowEventRuleSpyNewsStackSpyNewsLambdaFunction57319F6CB75C3916"}], "/SpyNewsStack/MarketPredictionTopicARN": [{"type": "aws:cdk:logicalId", "data": "MarketPredictionTopicARN"}], "/SpyNewsStack/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/SpyNewsStack/BootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "BootstrapVersion"}], "/SpyNewsStack/CheckBootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "CheckBootstrapVersion"}]}, "displayName": "SpyNewsStack"}, "Tree": {"type": "cdk:tree", "properties": {"file": "tree.json"}}}, "minimumCliVersion": "2.1005.0"}