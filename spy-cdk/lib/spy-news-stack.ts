import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as events from 'aws-cdk-lib/aws-events';
import * as targets from 'aws-cdk-lib/aws-events-targets';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as subscriptions from 'aws-cdk-lib/aws-sns-subscriptions';
import * as ses from 'aws-cdk-lib/aws-ses';
import * as sesActions from 'aws-cdk-lib/aws-ses-actions';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import * as path from 'path';
import * as dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

export class SpyNewsStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // We'll use existing SSM parameters instead of creating them in the CDK stack
    // The parameters should be created manually using the AWS CLI:
    // aws ssm put-parameter --name "/spy-news/sonar-api-key" --value "your-perplexity-api-key" --type String --overwrite
    // aws ssm put-parameter --name "/spy-news/finnhub-api-key" --value "your-finnhub-api-key" --type String --overwrite

    // Create a new SNS topic for market predictions
    const marketPredictionTopic = new sns.Topic(this, 'MarketPredictionTopic', {
      topicName: 'spy-news-market-prediction',
      displayName: 'SPY Market Predictions',
    });

    // Lambda execution role with permissions for SNS, SES, SSM and API calls
    const lambdaRole = new iam.Role(this, 'SpyNewsLambdaRole', {
      assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
      description: 'Role for the SPY News Lambda function',
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole'),
      ],
    });

    // Add necessary permissions to the role
    lambdaRole.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'sns:Publish',
        'ses:SendEmail',
        'ses:SendRawEmail',
        'ssm:GetParameter',
        'ssm:GetParameters'
      ],
      resources: ['*'],
    }));

    // Create the Lambda function
    const spyNewsLambda = new lambda.Function(this, 'SpyNewsLambdaFunction', {
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset(path.join(__dirname, '../lambda-assets/lambda-deployment.zip')),
      timeout: cdk.Duration.minutes(10), // Increased timeout for multiple API calls
      memorySize: 512, // Increased memory for better performance
      role: lambdaRole,
      environment: {
        SNS_TOPIC_ARN: marketPredictionTopic.topicArn,
        RECIPIENT_EMAILS: '<EMAIL>, <EMAIL>', // Comma-separated list of email addresses - add more emails separated by commas
        SENDER_EMAIL: '<EMAIL>', // Replace with verified SES email
        SONAR_API_KEY_PARAM: '/spy-news/sonar-api-key', // Parameter name in SSM for Perplexity API key
        FINNHUB_API_KEY_PARAM: '/spy-news/finnhub-api-key', // Parameter name in SSM for Finnhub API key
      },
    });

    // Create a Lambda function URL with CORS enabled
    const functionUrl = spyNewsLambda.addFunctionUrl({
      authType: lambda.FunctionUrlAuthType.NONE, // Public access
      cors: {
        allowedOrigins: ['*'], // Allow all origins
        allowedMethods: [lambda.HttpMethod.GET, lambda.HttpMethod.POST, lambda.HttpMethod.HEAD, lambda.HttpMethod.DELETE, lambda.HttpMethod.PUT, lambda.HttpMethod.PATCH],
        allowedHeaders: ['Content-Type', 'Authorization'],
        allowCredentials: false,
      },
    });

    // Output the Lambda function URL
    new cdk.CfnOutput(this, 'LambdaFunctionUrl', {
      value: functionUrl.url,
      description: 'URL of the Lambda function',
      exportName: 'SpyNewsLambdaUrl',
    });

    // Grant the Lambda function permission to publish to the SNS topic
    marketPredictionTopic.grantPublish(spyNewsLambda);

    // Schedule the Lambda to run at 9:00 AM ET on weekdays (trading days)
    // During Standard Time (EST): 9:00 AM EST = 14:00 UTC (UTC-5)
    // During Daylight Saving Time (EDT): 9:00 AM EDT = 13:00 UTC (UTC-4)
    // Using 13:00 UTC to ensure it runs at 9:00 AM ET during Daylight Saving Time
    const rule = new events.Rule(this, 'ScheduleRule', {
      schedule: events.Schedule.cron({
        minute: '0',
        hour: '13',
        weekDay: 'MON-FRI'
      }),
      description: 'Run SPY News analysis at 9:00 AM ET on weekdays (trading days)',
    });

    // Add the Lambda as a target to the rule
    rule.addTarget(new targets.LambdaFunction(spyNewsLambda));

    // Create an SNS email subscription
    marketPredictionTopic.addSubscription(
      new subscriptions.EmailSubscription('<EMAIL>') // Replace with actual email
    );

    // Output the SNS topic ARN
    new cdk.CfnOutput(this, 'MarketPredictionTopicARN', {
      value: marketPredictionTopic.topicArn,
      description: 'ARN of the Market Prediction SNS topic',
      exportName: 'SpyNewsTopicARN',
    });
  }
}