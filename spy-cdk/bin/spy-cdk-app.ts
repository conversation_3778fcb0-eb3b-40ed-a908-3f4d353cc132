#!/usr/bin/env node
import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import { SpyNewsStack } from '../lib/spy-news-stack';

const app = new cdk.App();

new SpyNewsStack(app, 'SpyNewsStack', {
  env: { 
    account: process.env.CDK_DEFAULT_ACCOUNT, 
    region: process.env.CDK_DEFAULT_REGION || 'us-east-1'
  },
  tags: {
    application: 'spy-news-aggregator',
    environment: 'production'
  }
});