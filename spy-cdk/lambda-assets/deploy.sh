#!/bin/bash

# Build TypeScript files
echo "Building TypeScript files..."
npm run build

# Install production dependencies only
echo "Installing production dependencies..."
npm install --only=production

# Create a deployment package
echo "Creating Lambda deployment package..."
mkdir -p dist-lambda
cp -r dist/* dist-lambda/
cp -r node_modules dist-lambda/
cd dist-lambda

# Zip the package
echo "Zipping Lambda package..."
zip -r ../lambda-deployment.zip .

cd ..

echo "Lambda deployment package created: lambda-deployment.zip"