import { SSMClient, GetParameterCommand } from '@aws-sdk/client-ssm';
import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses';
import { SNSClient, PublishCommand } from '@aws-sdk/client-sns';
import axios from 'axios';

// Initialize AWS clients
const ssmClient = new SSMClient({ region: 'us-east-1' });
const sesClient = new SESClient({ region: 'us-east-1' });
const snsClient = new SNSClient({ region: 'us-east-1' });

// Helper function to get Sonar API key from SSM
async function getSonarApiKey(): Promise<string> {
  try {
    const command = new GetParameterCommand({
      Name: '/spy-news/sonar-api-key',
      WithDecryption: true
    });
    const response = await ssmClient.send(command);
    return response.Parameter?.Value || '';
  } catch (error) {
    console.error('Error fetching Sonar API key from SSM:', error);
    throw new Error('Failed to retrieve Sonar API key');
  }
}

// Helper function to add CORS headers
function addCorsHeaders(response: any): any {
  const origin = 'http://localhost:5173'; // Set specific origin for development
  
  return {
    ...response,
    headers: {
      ...response.headers,
      'Access-Control-Allow-Origin': origin,
      'Access-Control-Allow-Methods': 'GET,POST,OPTIONS,PUT,DELETE',
      'Access-Control-Allow-Headers': 'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token',
      'Access-Control-Max-Age': '86400'
    }
  };
}

// Function to fetch news and market data
async function fetchNewsAndMarketData() {
  // Mock implementation - replace with actual news fetching logic
  const news = [
    {
      title: "Federal Reserve Signals Potential Rate Changes",
      source: "Financial Times",
      url: "https://example.com/fed-signals"
    },
    {
      title: "S&P 500 Companies Report Strong Earnings",
      source: "Wall Street Journal", 
      url: "https://example.com/earnings-strong"
    }
  ];

  const marketData = {
    spyPrice: 450.25,
    change: 2.15,
    volume: 85000000
  };

  return { news, marketData };
}

// Function to call Sonar API for market prediction
async function callSonarAPI(newsContext: string): Promise<string> {
  try {
    const SONAR_API_KEY = await getSonarApiKey();
    
    const prompt = `Based on the following recent news about SPY (S&P 500 ETF) and market conditions, provide a comprehensive market analysis and prediction:

News Context:
${newsContext}

Please provide:
1. Market Analysis: Current market sentiment and key factors
2. SPY Price Prediction: Short-term (1-3 days) and medium-term (1-2 weeks) outlook
3. Options Strategy: Specific options trading recommendations with strike prices and expiration dates
4. Risk Assessment: Key risks and potential catalysts

Format your response in a clear, structured manner suitable for email delivery.`;

    const response = await axios.post('https://api.perplexity.ai/chat/completions', {
      model: 'llama-3.1-sonar-large-128k-online',
      messages: [
        {
          role: 'system',
          content: 'You are an expert financial analyst specializing in SPY (S&P 500 ETF) analysis and options trading strategies.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 2000,
      temperature: 0.1
    }, {
      headers: {
        'Authorization': `Bearer ${SONAR_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    return response.data.choices[0].message.content;
  } catch (error) {
    console.error('Error calling Sonar API:', error);
    throw new Error('Failed to generate market prediction');
  }
}

// Function to send email via SES
async function sendEmail(prediction: string, news: any[], marketData: any) {
  try {
    const emailBody = `
SPY Market Analysis and Prediction

${prediction}

Recent News:
${news.map(item => `• ${item.title} (${item.source})`).join('\n')}

Current Market Data:
• SPY Price: $${marketData.spyPrice}
• Change: ${marketData.change > 0 ? '+' : ''}${marketData.change}
• Volume: ${marketData.volume.toLocaleString()}

Generated on: ${new Date().toLocaleString()}
    `;

    const command = new SendEmailCommand({
      Source: process.env.SENDER_EMAIL || '<EMAIL>',
      Destination: {
        ToAddresses: [process.env.RECIPIENT_EMAIL || '<EMAIL>']
      },
      Message: {
        Subject: {
          Data: `SPY Market Analysis - ${new Date().toLocaleDateString()}`
        },
        Body: {
          Text: {
            Data: emailBody
          }
        }
      }
    });

    await sesClient.send(command);
    console.log('Email sent successfully');
  } catch (error) {
    console.error('Error sending email:', error);
    throw new Error('Failed to send email');
  }
}

// Function to publish to SNS
async function publishToSNS(prediction: string) {
  try {
    const command = new PublishCommand({
      TopicArn: process.env.SNS_TOPIC_ARN,
      Message: `SPY Market Prediction: ${prediction.substring(0, 200)}...`,
      Subject: 'SPY Market Analysis Update'
    });

    await snsClient.send(command);
    console.log('SNS notification sent successfully');
  } catch (error) {
    console.error('Error publishing to SNS:', error);
    throw new Error('Failed to publish SNS notification');
  }
}

// Main handler function
export const handler = async (event: any): Promise<any> => {
  // Handle OPTIONS requests explicitly
  if (event.requestContext?.http?.method === 'OPTIONS' ||
      event.httpMethod === 'OPTIONS' ||
      (event.headers && event.headers['Access-Control-Request-Method'])) {
    console.log('Handling OPTIONS request');
    
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': 'http://localhost:5173',
        'Access-Control-Allow-Methods': 'GET,POST,OPTIONS,PUT,DELETE',
        'Access-Control-Allow-Headers': 'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Max-Age': '86400'
      },
      body: ''
    };
  }

  try {
    console.log('Executing SPY News Lambda function');
    
    // Get the Sonar API key from SSM
    const SONAR_API_KEY = await getSonarApiKey();
    
    // 1. Fetch news and market data
    const { news, marketData } = await fetchNewsAndMarketData();
    
    // Prepare the news context for the API
    const newsContext = news.map(item => `${item.title} (${item.source}): ${item.url}`).join('\n');
    
    // 2. Call Sonar API to get market prediction
    console.log('Calling Sonar API to generate market prediction...');
    const marketPrediction = await callSonarAPI(newsContext);
    console.log('Market prediction generated successfully');

    // 3. Send email with the prediction
    await sendEmail(marketPrediction, news, marketData);
    
    // 4. Publish to SNS
    await publishToSNS(marketPrediction);

    const response = {
      statusCode: 200,
      body: JSON.stringify({
        message: 'SPY News analysis completed successfully',
        prediction: marketPrediction,
        newsCount: news.length,
        marketData: marketData
      })
    };

    return addCorsHeaders(response);
  } catch (error) {
    console.error('Error in Lambda function:', error);
    
    const errorResponse = {
      statusCode: 500,
      body: JSON.stringify({
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      })
    };

    return addCorsHeaders(errorResponse);
  }
};
