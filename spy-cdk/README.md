# SPY News Aggregator CDK Project

This AWS CDK project sets up infrastructure for automatically analyzing SPY (S&P 500 ETF) news and market data, generating predictions using Perplexity's Sonar API, and sending the results via email.

## Architecture

The project deploys the following AWS resources:

- **Lambda Function**: Runs daily at 9:00 AM on trading days (Monday-Friday)
- **EventBridge Rule**: Schedules the Lambda function
- **SNS Topic**: Publishes market predictions and options strategies
- **IAM Role**: Grants the Lambda function necessary permissions
- **SES Configuration**: Sends formatted emails with the analysis results

## Prerequisites

1. AWS CLI configured with appropriate credentials
2. Node.js 18.x or later
3. Perplexity Sonar API key
4. Verified email addresses in SES (for sender and recipient)

## Deployment

1. Install dependencies:
   ```bash
   npm install
   ```

2. Build the project:
   ```bash
   npm run build
   ```

3. Set up environment variables for the Lambda function:
   - SONAR_API_KEY: Your Perplexity Sonar API key
   - RECIPIENT_EMAIL: The email address to receive predictions
   - SENDER_EMAIL: The verified SES email address to send from

4. Deploy the stack:
   ```bash
   npm run deploy
   ```

## Configuration

You can customize the deployment by modifying:

- **lib/spy-news-stack.ts**: Main infrastructure definitions
- **lambda-assets/index.ts**: Lambda function code
- **bin/spy-cdk-app.ts**: CDK application entry point

## Lambda Function

The Lambda function performs the following:

1. Fetches recent news articles and market data
2. Analyzes the data using Perplexity's Sonar API
3. Generates market predictions and options trading strategies
4. Sends formatted results via email and publishes a notification to SNS

## Email Format

The email includes:
- Market prediction analysis (plain text, no markdown)
- Options trading strategy with visualization
- Related stock recommendations
- Risk/reward details
- Latest news articles with sources and links

## Customization

To customize the analysis:
- Modify the prompts in the Lambda code
- Update the email template formatting
- Change the schedule in the EventBridge rule