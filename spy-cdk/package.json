{"name": "spy-cdk", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc", "watch": "tsc -w", "cdk": "cdk", "deploy": "cdk deploy"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@aws-sdk/client-ses": "^3.777.0", "@aws-sdk/client-sns": "^3.777.0", "@types/node": "^22.14.0", "aws-cdk": "^2.1007.0", "aws-cdk-lib": "^2.187.0", "constructs": "^10.4.2", "dotenv": "^16.3.1", "source-map-support": "^0.5.21", "ts-node": "^10.9.2", "typescript": "^5.8.2"}}