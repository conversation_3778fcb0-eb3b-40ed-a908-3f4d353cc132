#!/bin/bash

# Exit on error
set -e

# Get the Lambda function name
LAMBDA_FUNCTION_NAME="SpyNewsStack-SpyNewsLambdaFunctionAAE3CD75-5Y9EPKJoj6De"

# Get the Lambda function URL ID
echo "Getting Lambda function URL ID..."
FUNCTION_URL_ID=$(aws lambda list-function-url-configs --function-name $LAMBDA_FUNCTION_NAME --query "FunctionUrlConfigs[0].FunctionUrlId" --output text)

if [ -z "$FUNCTION_URL_ID" ]; then
  echo "No function URL found. Creating a new one with CORS enabled..."
  aws lambda create-function-url-config \
    --function-name $LAMBDA_FUNCTION_NAME \
    --auth-type NONE \
    --cors '{"AllowOrigins": ["*"], "AllowMethods": ["GET", "POST", "OPTIONS"], "AllowHeaders": ["Content-Type", "Authorization"], "AllowCredentials": false}'
  
  echo "Function URL created successfully with CORS enabled."
else
  echo "Updating existing function URL with CORS configuration..."
  aws lambda update-function-url-config \
    --function-name $LAMBDA_FUNCTION_NAME \
    --function-url-id $FUNCTION_URL_ID \
    --cors '{"AllowOrigins": ["*"], "AllowMethods": ["GET", "POST", "OPTIONS"], "AllowHeaders": ["Content-Type", "Authorization"], "AllowCredentials": false}'
  
  echo "Function URL updated successfully with CORS enabled."
fi

# Get and display the function URL
FUNCTION_URL=$(aws lambda list-function-url-configs --function-name $LAMBDA_FUNCTION_NAME --query "FunctionUrlConfigs[0].FunctionUrl" --output text)
echo "Lambda function URL: $FUNCTION_URL"