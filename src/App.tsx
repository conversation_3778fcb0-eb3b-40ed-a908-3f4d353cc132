import React, { useState, useEffect } from 'react';
import { NewsItem } from './types/news';
import { CatalystEvent, catalystService } from './services/catalystService';
import { newsService } from './services/newsService';
import { predictionService } from './services/predictionService';
import NewsCarousel from './components/NewsCarousel';
import PredictionDisplay from './components/PredictionDisplay';
import CatalystDisplay from './components/CatalystDisplay';

function App() {
  const [newsItems, setNewsItems] = useState<NewsItem[]>([]);
  const [newsContext, setNewsContext] = useState<string>('');
  const [marketPrediction, setMarketPrediction] = useState<string>('');
  const [optionsStrategy, setOptionsStrategy] = useState<string>('');
  const [catalysts, setCatalysts] = useState<CatalystEvent[]>([]);
  const [catalystSummary, setCatalystSummary] = useState<string>('');
  const [nextCatalyst, setNextCatalyst] = useState<CatalystEvent | null>(null);
  const [activeSlide, setActiveSlide] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Load cached data on component mount
  useEffect(() => {
    const cachedNews = localStorage.getItem('newsItems');
    const cachedContext = localStorage.getItem('newsContext');
    const cachedPrediction = localStorage.getItem('marketPrediction');
    const cachedStrategy = localStorage.getItem('optionsStrategy');
    const cachedSlide = localStorage.getItem('activeSlide');

    if (cachedNews) setNewsItems(JSON.parse(cachedNews));
    if (cachedContext) setNewsContext(cachedContext);
    if (cachedPrediction) setMarketPrediction(cachedPrediction);
    if (cachedStrategy) setOptionsStrategy(cachedStrategy);
    if (cachedSlide) setActiveSlide(parseInt(cachedSlide));
  }, []);

  // Save data to localStorage whenever state changes
  useEffect(() => {
    if (newsItems.length > 0) {
      localStorage.setItem('newsItems', JSON.stringify(newsItems));
    }
  }, [newsItems]);

  useEffect(() => {
    if (newsContext) {
      localStorage.setItem('newsContext', newsContext);
    }
  }, [newsContext]);

  useEffect(() => {
    if (marketPrediction) {
      localStorage.setItem('marketPrediction', marketPrediction);
    }
  }, [marketPrediction]);

  useEffect(() => {
    if (optionsStrategy) {
      localStorage.setItem('optionsStrategy', optionsStrategy);
    }
  }, [optionsStrategy]);

  useEffect(() => {
    localStorage.setItem('activeSlide', activeSlide.toString());
  }, [activeSlide]);

  const fetchNews = async () => {
    try {
      setIsLoading(true);
      console.log('Fetching news...');
      const news = await newsService.getLatestNews();
      setNewsItems(news);
      
      const context = news.map(item => `${item.title}: ${item.summary}`).join('\n\n');
      setNewsContext(context);
      console.log('News fetched successfully');
    } catch (error) {
      console.error('Error fetching news:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const generatePrediction = async () => {
    if (!newsContext) {
      alert('Please fetch news first');
      return;
    }

    try {
      setIsLoading(true);
      console.log('Generating market prediction...');
      const prediction = await predictionService.generateMarketPrediction(newsContext);
      setMarketPrediction(prediction);
      console.log('Market prediction generated successfully');
    } catch (error) {
      console.error('Error generating prediction:', error);
      alert('Error generating prediction. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const generateOptionsStrategy = async () => {
    if (!marketPrediction) {
      alert('Please generate market prediction first');
      return;
    }

    try {
      setIsLoading(true);
      console.log('Generating options strategy...');
      const strategy = await predictionService.generateOptionsStrategy(marketPrediction, newsContext);
      setOptionsStrategy(strategy);
      console.log('Options strategy generated successfully');
    } catch (error) {
      console.error('Error generating options strategy:', error);
      alert('Error generating options strategy. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const loadCatalysts = async () => {
    try {
      setIsLoading(true);
      console.log('Loading catalyst data...');
      
      const [allCatalysts, nextMajorCatalyst] = await Promise.all([
        catalystService.getAllUpcomingCatalysts(),
        catalystService.getNextMajorCatalyst()
      ]);
      
      setCatalysts(allCatalysts);
      setNextCatalyst(nextMajorCatalyst);
      
      if (allCatalysts.length > 0) {
        const summary = await catalystService.getCatalystSummary(allCatalysts);
        setCatalystSummary(summary);
      }
      
      console.log('Catalyst data loaded successfully');
    } catch (error) {
      console.error('Error loading catalysts:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Clear all cached data and load catalysts
  const clearAllData = () => {
    localStorage.clear();
    
    // Reset all state
    setNewsItems([]);
    setNewsContext('');
    setMarketPrediction('');
    setOptionsStrategy('');
    setCatalysts([]);
    setCatalystSummary('');
    setNextCatalyst(null);
    setActiveSlide(0);
    
    // Load fresh catalyst data
    loadCatalysts();
    
    // Fetch new news data
    fetchNews();
  };

  // Restart the entire process with fresh catalyst data
  const restartProcess = () => {
    // Clear all analysis data but keep settings
    localStorage.removeItem('newsItems');
    localStorage.removeItem('newsContext');
    localStorage.removeItem('marketPrediction');
    localStorage.removeItem('optionsStrategy');
    localStorage.removeItem('activeSlide');

    // Reset state
    setNewsItems([]);
    setNewsContext('');
    setMarketPrediction('');
    setOptionsStrategy('');
    setCatalysts([]);
    setCatalystSummary('');
    setNextCatalyst(null);
    setActiveSlide(0);

    // Load fresh catalyst data
    loadCatalysts();
    
    // Fetch new data
    fetchNews();
  };

  const runFullAnalysis = async () => {
    try {
      setIsLoading(true);
      
      // Step 1: Fetch news
      await fetchNews();
      
      // Step 2: Generate prediction
      const news = await newsService.getLatestNews();
      const context = news.map(item => `${item.title}: ${item.summary}`).join('\n\n');
      const prediction = await predictionService.generateMarketPrediction(context);
      setMarketPrediction(prediction);
      
      // Step 3: Generate options strategy
      const strategy = await predictionService.generateOptionsStrategy(prediction, context);
      setOptionsStrategy(strategy);
      
      console.log('Full analysis completed successfully');
    } catch (error) {
      console.error('Error running full analysis:', error);
      alert('Error running full analysis. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            📈 SPY News Aggregator & Market Predictor
          </h1>
          <p className="text-gray-600 text-lg">
            AI-powered analysis of SPY market trends and trading opportunities
          </p>
        </header>

        <div className="flex flex-wrap gap-4 justify-center mb-8">
          <button
            onClick={fetchNews}
            disabled={isLoading}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors font-medium"
          >
            📰 Fetch Latest News
          </button>
          
          <button
            onClick={generatePrediction}
            disabled={isLoading || !newsContext}
            className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors font-medium"
          >
            🔮 Generate Prediction
          </button>
          
          <button
            onClick={generateOptionsStrategy}
            disabled={isLoading || !marketPrediction}
            className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 transition-colors font-medium"
          >
            📊 Options Strategy
          </button>
          
          <button
            onClick={loadCatalysts}
            disabled={isLoading}
            className="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 transition-colors font-medium"
          >
            📅 Load Catalysts
          </button>
          
          <button
            onClick={runFullAnalysis}
            disabled={isLoading}
            className="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 transition-colors font-medium"
          >
            🚀 Full Analysis
          </button>
          
          <button
            onClick={restartProcess}
            className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium"
          >
            🔄 Restart Everything
          </button>
          
          <button
            onClick={clearAllData}
            className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium"
          >
            🗑️ Clear Cache
          </button>
        </div>

        {isLoading && (
          <div className="text-center mb-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-2 text-gray-600">Processing...</p>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            {newsItems.length > 0 && (
              <NewsCarousel 
                newsItems={newsItems}
                activeSlide={activeSlide}
                onSlideChange={setActiveSlide}
              />
            )}
            
            {(marketPrediction || optionsStrategy) && (
              <PredictionDisplay 
                marketPrediction={marketPrediction}
                optionsStrategy={optionsStrategy}
              />
            )}
          </div>
          
          <div>
            {(catalysts.length > 0 || nextCatalyst) && (
              <CatalystDisplay 
                catalysts={catalysts}
                catalystSummary={catalystSummary}
                nextCatalyst={nextCatalyst}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
                                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                                Refresh
                              </button>
                            </div>
                          </div>
                          <div className="p-4">
                            {marketPrediction ? (
                              <>
                                <ParsedMarketAnalysis rawData={marketPrediction} />
                                {/* Fallback display if parsing fails */}
                                <details className="mt-3 text-sm text-gray-500 no-print">
                                  <summary className="cursor-pointer text-xs text-gray-500">View Raw Data</summary>
                                  <pre className="mt-2 p-2 bg-gray-100 rounded overflow-auto text-xs max-h-40">
                                    {marketPrediction}
                                  </pre>
                                </details>
                              </>
                            ) : (
                              <div className="text-gray-500">No market analysis data available</div>
                            )}
                          </div>
                        </div>

                        {/* News Card */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                          <div className="bg-blue-50 px-4 py-2 border-b border-blue-100">
                            <div className="flex justify-between items-center">
                              <h3 className="text-md font-semibold text-blue-800 flex items-center">
                                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                                </svg>
                                Key Market News
                              </h3>
                              <button
                                onClick={() => {
                                  // Reset to the news step and refresh news data
                                  setActiveSlide(0);
                                  refreshNews();
                                }}
                                className="px-2 py-1 text-xs rounded flex items-center bg-blue-100 text-blue-700 hover:bg-blue-200 transition-all no-print"
                              >
                                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                                Refresh
                              </button>
                            </div>
                          </div>
                          <div className="p-4">
                            <div className="space-y-3">
                              {newsItems.slice(0, 3).map((item, index) => (
                                <div key={index} className="bg-gray-50 p-4 rounded-md border border-gray-100 hover:shadow-md transition-all">
                                  <div className="font-medium text-sm mb-2">{item.title}</div>
                                  <div className="text-sm text-gray-600 mb-3 line-clamp-3">{item.content}</div>
                                  <div className="flex justify-between items-center mt-2 text-xs">
                                    <div className="flex items-center">
                                      <svg className="w-3 h-3 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                                      </svg>
                                      <span className="text-gray-500">{item.source}</span>
                                    </div>
                                    <span className={`px-2 py-1 rounded-full ${
                                      item.impact === 'high' ? 'bg-red-100 text-red-800' :
                                      item.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                      'bg-green-100 text-green-800'
                                    }`}>
                                      {item.impact} impact
                                    </span>
                                  </div>
                                  {item.url && (
                                    <div className="mt-3 text-right">
                                      <a
                                        href={item.url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-xs text-blue-600 hover:text-blue-800 hover:underline flex items-center justify-end"
                                      >
                                        <span>Read full article</span>
                                        <svg className="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2-2V6a2 2 0 002-2h10a2 2 0 002 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                                        </svg>
                                      </a>
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Right column - Options Strategy and Follow-up */}
                      <div className="space-y-4 animate-fade-in animation-delay-200">
                        {/* Options Strategy Card */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                          <div className="bg-green-50 px-4 py-2 border-b border-green-100">
                            <div className="flex justify-between items-center">
                              <h3 className="text-md font-semibold text-green-800 flex items-center">
                                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
                                </svg>
                                Options Strategy
                              </h3>
                              <button
                                onClick={() => {
                                  // Reset options strategy and move to strategy slide
                                  setOptionsStrategy('');
                                  localStorage.removeItem('optionsStrategy');
                                  setActiveSlide(2);
                                  setStrategyLoading(true);
                                }}
                                className="px-2 py-1 text-xs rounded flex items-center bg-green-100 text-green-700 hover:bg-green-200 transition-all no-print"
                              >
                                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                                Refresh
                              </button>
                            </div>
                          </div>
                          <div className="p-4">
                            {optionsStrategy ? (
                              <>
                                <ParsedOptionsStrategy rawData={optionsStrategy} />
                                {/* Fallback display if parsing fails */}
                                <details className="mt-3 text-sm text-gray-500 no-print">
                                  <summary className="cursor-pointer text-xs text-gray-500">View Raw Data</summary>
                                  <pre className="mt-2 p-2 bg-gray-100 rounded overflow-auto text-xs max-h-40">
                                    {optionsStrategy}
                                  </pre>
                                </details>
                              </>
                            ) : (
                              <div className="text-gray-500">No options strategy data available</div>
                            )}
                          </div>
                        </div>

                        {/* Upcoming Catalysts Timeline */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden animate-fade-in animation-delay-250">
                          <div className="bg-orange-50 px-4 py-2 border-b border-orange-100">
                            <h3 className="text-md font-semibold text-orange-800 flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              Upcoming Market Catalysts
                            </h3>
                          </div>
                          <div className="p-4">
                            <UpcomingCatalysts 
                              catalysts={catalysts}
                              isLoading={catalystsLoading}
                              maxEvents={25}
                              showHeader={false}
                              compact={true}
                            />
                          </div>
                        </div>

                        {/* Follow-up Questions Section */}
                        {marketPrediction && optionsStrategy && newsContext && (
                          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden animate-fade-in animation-delay-300">
                            <div className="bg-indigo-50 px-4 py-2 border-b border-indigo-100">
                              <h3 className="text-md font-semibold text-indigo-800 flex items-center">
                                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                Follow-up Questions
                              </h3>
                            </div>
                            <div className="p-4">
                              <FollowUpQuestions
                                marketPrediction={marketPrediction}
                                optionsStrategy={optionsStrategy}
                                newsContext={newsContext}
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Tools Fixed Button */}
      <div className="fixed bottom-5 right-5 z-40">
        <div className="relative">
          <button
            onClick={toggleDebugger}
            className="flex items-center justify-center p-3 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg transition-all"
            title="Tools"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </button>

          {showDebugger && (
            <div className="absolute bottom-full right-0 mb-2 w-80 bg-white rounded-lg shadow-lg overflow-hidden animate-slide-up">
              {/* Debugging UI */}
              <div className="p-4 border-b border-gray-200">
                <h3 className="font-medium text-gray-700 mb-2">Debugging Tools</h3>
                <div className="space-y-2">
                  <ApiDebugger
                    isVisible={true}
                    apiKey={import.meta.env.VITE_FINNHUB_API_KEY}
                  />
                  <MarketDataDebugger
                    isVisible={true}
                    marketData={marketData}
                    isLoading={isLoading}
                    isError={isError}
                    lastRefreshTime={lastManualRefresh || marketData?.lastUpdate || ''}
                    onRefresh={forceRefreshMarketData}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <footer className="bg-gray-800 text-white py-6">
        <div className="container mx-auto px-4 text-center">
          <p className="text-sm">
            SPY News Aggregator | For educational purposes only | Not financial advice
          </p>
          <p className="text-xs text-gray-400 mt-1">
            © {new Date().getFullYear()} SPY News Aggregator. All rights reserved.
          </p>
        </div>
      </footer>

      {/* Add CSS for animations */}
      <style>{`
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes slideUp {
          from { transform: translateY(20px); opacity: 0; }
          to { transform: translateY(0); opacity: 1; }
        }

        .animate-fade-in {
          animation: fadeIn 0.5s ease-in-out forwards;
        }

        .animate-slide-up {
          animation: slideUp 0.3s ease-out forwards;
        }

        .animation-delay-100 {
          animation-delay: 0.1s;
        }

        .animation-delay-200 {
          animation-delay: 0.2s;
        }

        .animation-delay-300 {
          animation-delay: 0.3s;
        }

        /* Line clamp utilities */
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .line-clamp-3 {
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        /* Print styles */
        @media print {
          .no-print {
            display: none !important;
          }

          body {
            font-size: 12pt;
          }

          .print-break-inside-avoid {
            break-inside: avoid;
          }
        }
      `}</style>
    </div>
  );
}

// Create a single instance of QueryClient
const queryClient = new QueryClient();

// Wrapper component that provides the QueryClient
export default function WrappedApp() {
  return (
    <QueryClientProvider client={queryClient}>
      <App />
    </QueryClientProvider>
  );
}
