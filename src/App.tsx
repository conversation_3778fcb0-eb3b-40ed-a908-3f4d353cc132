import React, { useState, useEffect } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { NewsItem } from './types/news';
import { CatalystEvent, catalystService } from './services/catalystService';
import { newsService } from './services/newsService';
import { predictionService } from './services/predictionService';
import NewsCarousel from './components/NewsCarousel';
import PredictionDisplay from './components/PredictionDisplay';
import CatalystDisplay from './components/CatalystDisplay';

function App() {
  const [newsItems, setNewsItems] = useState<NewsItem[]>([]);
  const [newsContext, setNewsContext] = useState<string>('');
  const [marketPrediction, setMarketPrediction] = useState<string>('');
  const [optionsStrategy, setOptionsStrategy] = useState<string>('');
  const [catalysts, setCatalysts] = useState<CatalystEvent[]>([]);
  const [catalystSummary, setCatalystSummary] = useState<string>('');
  const [nextCatalyst, setNextCatalyst] = useState<CatalystEvent | null>(null);
  const [activeSlide, setActiveSlide] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Load cached data on component mount
  useEffect(() => {
    const cachedNews = localStorage.getItem('newsItems');
    const cachedContext = localStorage.getItem('newsContext');
    const cachedPrediction = localStorage.getItem('marketPrediction');
    const cachedStrategy = localStorage.getItem('optionsStrategy');
    const cachedSlide = localStorage.getItem('activeSlide');

    if (cachedNews) setNewsItems(JSON.parse(cachedNews));
    if (cachedContext) setNewsContext(cachedContext);
    if (cachedPrediction) setMarketPrediction(cachedPrediction);
    if (cachedStrategy) setOptionsStrategy(cachedStrategy);
    if (cachedSlide) setActiveSlide(parseInt(cachedSlide));
  }, []);

  // Save data to localStorage whenever state changes
  useEffect(() => {
    if (newsItems.length > 0) {
      localStorage.setItem('newsItems', JSON.stringify(newsItems));
    }
  }, [newsItems]);

  useEffect(() => {
    if (newsContext) {
      localStorage.setItem('newsContext', newsContext);
    }
  }, [newsContext]);

  useEffect(() => {
    if (marketPrediction) {
      localStorage.setItem('marketPrediction', marketPrediction);
    }
  }, [marketPrediction]);

  useEffect(() => {
    if (optionsStrategy) {
      localStorage.setItem('optionsStrategy', optionsStrategy);
    }
  }, [optionsStrategy]);

  useEffect(() => {
    localStorage.setItem('activeSlide', activeSlide.toString());
  }, [activeSlide]);

  const fetchNews = async () => {
    try {
      setIsLoading(true);
      console.log('Fetching news...');
      const news = await newsService.getLatestNews();
      setNewsItems(news);
      
      const context = news.map(item => `${item.title}: ${item.summary}`).join('\n\n');
      setNewsContext(context);
      console.log('News fetched successfully');
    } catch (error) {
      console.error('Error fetching news:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const generatePrediction = async () => {
    if (!newsContext) {
      alert('Please fetch news first');
      return;
    }

    try {
      setIsLoading(true);
      console.log('Generating market prediction...');
      const prediction = await predictionService.generateMarketPrediction(newsContext);
      setMarketPrediction(prediction);
      console.log('Market prediction generated successfully');
    } catch (error) {
      console.error('Error generating prediction:', error);
      alert('Error generating prediction. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const generateOptionsStrategy = async () => {
    if (!marketPrediction) {
      alert('Please generate market prediction first');
      return;
    }

    try {
      setIsLoading(true);
      console.log('Generating options strategy...');
      const strategy = await predictionService.generateOptionsStrategy(marketPrediction, newsContext);
      setOptionsStrategy(strategy);
      console.log('Options strategy generated successfully');
    } catch (error) {
      console.error('Error generating options strategy:', error);
      alert('Error generating options strategy. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const loadCatalysts = async () => {
    try {
      setIsLoading(true);
      console.log('Loading catalyst data...');
      
      const [allCatalysts, nextMajorCatalyst] = await Promise.all([
        catalystService.getAllUpcomingCatalysts(),
        catalystService.getNextMajorCatalyst()
      ]);
      
      setCatalysts(allCatalysts);
      setNextCatalyst(nextMajorCatalyst);
      
      if (allCatalysts.length > 0) {
        const summary = await catalystService.getCatalystSummary(allCatalysts);
        setCatalystSummary(summary);
      }
      
      console.log('Catalyst data loaded successfully');
    } catch (error) {
      console.error('Error loading catalysts:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Clear all cached data and load catalysts
  const clearAllData = () => {
    localStorage.clear();
    
    // Reset all state
    setNewsItems([]);
    setNewsContext('');
    setMarketPrediction('');
    setOptionsStrategy('');
    setCatalysts([]);
    setCatalystSummary('');
    setNextCatalyst(null);
    setActiveSlide(0);
    
    // Load fresh catalyst data
    loadCatalysts();
    
    // Fetch new news data
    fetchNews();
  };

  // Restart the entire process with fresh catalyst data
  const restartProcess = () => {
    // Clear all analysis data but keep settings
    localStorage.removeItem('newsItems');
    localStorage.removeItem('newsContext');
    localStorage.removeItem('marketPrediction');
    localStorage.removeItem('optionsStrategy');
    localStorage.removeItem('activeSlide');

    // Reset state
    setNewsItems([]);
    setNewsContext('');
    setMarketPrediction('');
    setOptionsStrategy('');
    setCatalysts([]);
    setCatalystSummary('');
    setNextCatalyst(null);
    setActiveSlide(0);

    // Load fresh catalyst data
    loadCatalysts();
    
    // Fetch new data
    fetchNews();
  };

  const runFullAnalysis = async () => {
    try {
      setIsLoading(true);
      
      // Step 1: Fetch news
      await fetchNews();
      
      // Step 2: Generate prediction
      const news = await newsService.getLatestNews();
      const context = news.map(item => `${item.title}: ${item.summary}`).join('\n\n');
      const prediction = await predictionService.generateMarketPrediction(context);
      setMarketPrediction(prediction);
      
      // Step 3: Generate options strategy
      const strategy = await predictionService.generateOptionsStrategy(prediction, context);
      setOptionsStrategy(strategy);
      
      console.log('Full analysis completed successfully');
    } catch (error) {
      console.error('Error running full analysis:', error);
      alert('Error running full analysis. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            📈 SPY News Aggregator & Market Predictor
          </h1>
          <p className="text-gray-600 text-lg">
            AI-powered analysis of SPY market trends and trading opportunities
          </p>
        </header>

        <div className="flex flex-wrap gap-4 justify-center mb-8">
          <button
            onClick={fetchNews}
            disabled={isLoading}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors font-medium"
          >
            📰 Fetch Latest News
          </button>
          
          <button
            onClick={generatePrediction}
            disabled={isLoading || !newsContext}
            className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors font-medium"
          >
            🔮 Generate Prediction
          </button>
          
          <button
            onClick={generateOptionsStrategy}
            disabled={isLoading || !marketPrediction}
            className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 transition-colors font-medium"
          >
            📊 Options Strategy
          </button>
          
          <button
            onClick={loadCatalysts}
            disabled={isLoading}
            className="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 transition-colors font-medium"
          >
            📅 Load Catalysts
          </button>
          
          <button
            onClick={runFullAnalysis}
            disabled={isLoading}
            className="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 transition-colors font-medium"
          >
            🚀 Full Analysis
          </button>
          
          <button
            onClick={restartProcess}
            className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium"
          >
            🔄 Restart Everything
          </button>
          
          <button
            onClick={clearAllData}
            className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium"
          >
            🗑️ Clear Cache
          </button>
        </div>

        {isLoading && (
          <div className="text-center mb-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-2 text-gray-600">Processing...</p>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            {newsItems.length > 0 && (
              <NewsCarousel 
                newsItems={newsItems}
                activeSlide={activeSlide}
                onSlideChange={setActiveSlide}
              />
            )}
            
            {(marketPrediction || optionsStrategy) && (
              <PredictionDisplay 
                marketPrediction={marketPrediction}
                optionsStrategy={optionsStrategy}
              />
            )}
          </div>
          
          <div>
            {(catalysts.length > 0 || nextCatalyst) && (
              <CatalystDisplay 
                catalysts={catalysts}
                catalystSummary={catalystSummary}
                nextCatalyst={nextCatalyst}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Create a single instance of QueryClient
const queryClient = new QueryClient();

// Wrapper component that provides the QueryClient
export default function WrappedApp() {
  return (
    <QueryClientProvider client={queryClient}>
      <App />
    </QueryClientProvider>
  );
}
