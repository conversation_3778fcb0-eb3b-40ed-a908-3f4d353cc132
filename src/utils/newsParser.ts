import { PerplexityResponse } from '../services/api';

export interface NewsItem {
  title: string;
  content: string;
  source?: string;
  timestamp?: string;
  impact: 'high' | 'medium' | 'low';
  url?: string;
}

export interface MarketPrediction {
  prediction: 'Bullish' | 'Bearish' | 'Neutral';
  expectedOpen: string;
  confidence: 'High' | 'Medium' | 'Low';
  analysis: string;
  keyFactors: string[];
  risks: string[];
  raw: string;
}

/**
 * Parses the Perplexity API response content into structured news items
 * Handles the new format with URLs
 */
export const parseNewsFromResponse = (response: PerplexityResponse): NewsItem[] => {
  if (!response?.choices?.length) {
    return [];
  }

  const content = response.choices[0].message.content;

  // Debug: Print entire API response for troubleshooting
  console.log("===== FULL SONAR API RESPONSE START =====");
  console.log(content);
  console.log("===== FULL SONAR API RESPONSE END =====");

  // Pre-process the content to normalize format
  // 1. Ensure SOURCE: and URL: lines are attached to their news items
  let processedContent = content;
  // Attach SOURCE lines to previous paragraph
  processedContent = processedContent.replace(/(\n+)(SOURCE\s*:.+)(\n+)/gi, "$1$2\n");
  // Attach IMPACT lines to previous paragraph
  processedContent = processedContent.replace(/(\n+)(IMPACT\s*:.+)(\n+)/gi, "$1$2\n");
  // Attach URL lines to previous paragraph - use a more robust regex for URLs
  processedContent = processedContent.replace(/(\n+)(URL\s*:\s*(?:https?:\/\/)?[^\s\r\n]+)(\n+)/gi, "$1$2\n");
  // Attach standalone URLs to previous paragraph - also be more flexible with URL format
  processedContent = processedContent.replace(/(\n+)((?:https?:\/\/)?[a-z0-9][-a-z0-9.]*\.[a-z]{2,}(?:\/[-\w./?&=+%#]*\S)?)(\n+)/gi, "$1$2\n");

  // For the new format, we'll split based on headlines or multiple line breaks
  const newsRegex = /(?:HEADLINE\s*:|(?:\r?\n){2,})/gm;
  const newsBlocks = processedContent.split(newsRegex)
    .filter(Boolean)
    .map(block => block.trim())
    .filter(block => block.length > 20); // Skip very short blocks

  // Debug: Log all blocks after splitting
  console.log("===== BLOCKS AFTER SPLITTING =====");
  newsBlocks.forEach((block, index) => {
    console.log(`Block ${index}:\n${block}\n---------`);
  });

  const newsItems: NewsItem[] = [];
  const urlOnlyBlocks: string[] = []; // Track standalone URL blocks
  const sourceOnlyBlocks: {source: string, url?: string}[] = [];

  // First pass: identify URL-only and SOURCE-only blocks
  for (let i = 0; i < newsBlocks.length; i++) {
    const block = newsBlocks[i];

    // Check if this is a source-only block (just starts with SOURCE: with no headline)
    const isSourceOnly = block.trim().match(/^SOURCE\s*:/i) &&
                        !block.match(/HEADLINE\s*:/i) &&
                        block.split('\n').length <= 3; // Only a few lines at most

    // Check if this block appears to be only a URL
    const isUrlOnly = block.match(/^(?:URL\s*:\s*)?(?:https?:\/\/)?[a-z0-9][-a-z0-9.]*\.[a-z]{2,}(?:\/[-\w./?&=+%#]*\S)?$/i) &&
                     !block.match(/HEADLINE\s*:/i) &&
                     block.split('\n').length <= 2;

    if (isSourceOnly) {
      console.log(`Identified SOURCE-only block ${i}: ${block}`);

      // Extract source from the block
      const sourceMatch = block.match(/SOURCE\s*:\s*([^\r\n]+)/i);
      let source = sourceMatch ? sourceMatch[1].trim() : '';

      // Try to extract URL if present
      let url = '';
      const urlMatch = block.match(/URL\s*:\s*((?:https?:\/\/)?[^\s\r\n]+)/i);
      if (urlMatch) {
        url = urlMatch[1].trim();
        // Ensure URL has http/https prefix
        if (!url.match(/^https?:\/\//i)) {
          url = 'https://' + url;
        }
        console.log(`Found URL in source-only block: ${url}`);
      } else {
        const generalUrlMatch = block.match(/(?:https?:\/\/)?[a-z0-9][-a-z0-9.]*\.[a-z]{2,}(?:\/[-\w./?&=+%#]*\S)?/i);
        if (generalUrlMatch) {
          url = generalUrlMatch[0].trim();
          // Ensure URL has http/https prefix
          if (!url.match(/^https?:\/\//i)) {
            url = 'https://' + url;
          }
          console.log(`Found general URL in source-only block: ${url}`);
        }
      }

      sourceOnlyBlocks.push({ source, url });
      newsBlocks[i] = ''; // Mark for removal
    } else if (isUrlOnly) {
      console.log(`Identified URL-only block ${i}: ${block}`);
      let url = block.replace(/^(?:URL|SOURCE)\s*:\s*/i, '').trim();
      // Ensure URL has http/https prefix
      if (!url.match(/^https?:\/\//i)) {
        url = 'https://' + url;
      }
      urlOnlyBlocks.push(url);
      newsBlocks[i] = ''; // Mark for removal
    }
  }

  // Debug: Log captured special blocks
  console.log("===== URL ONLY BLOCKS =====");
  urlOnlyBlocks.forEach((url, index) => {
    console.log(`URL ${index}: ${url}`);
  });

  console.log("===== SOURCE ONLY BLOCKS =====");
  sourceOnlyBlocks.forEach((item, index) => {
    console.log(`SOURCE ${index}: ${item.source}, URL: ${item.url || 'none'}`);
  });

  // Remove empty blocks
  const filteredBlocks = newsBlocks.filter(block => block.length > 0);

  // Debug: Log filtered blocks
  console.log("===== FILTERED BLOCKS =====");
  filteredBlocks.forEach((block, index) => {
    console.log(`Filtered Block ${index}:\n${block.substring(0, 100)}...\n---------`);
  });

  // Second pass: process valid news blocks
  for (let i = 0; i < filteredBlocks.length; i++) {
    const block = filteredBlocks[i];

    // Extract components from the standardized format
    let title = '';
    let source = '';
    let timestamp = '';
    let content = '';
    let url = '';
    let impact: 'high' | 'medium' | 'low' = 'medium'; // Default impact

    // Try to extract title from the first line
    const lines = block.split('\n');
    if (lines.length > 0) {
      title = lines[0].trim().replace(/^HEADLINE\s*:\s*/i, '');
    }

    // Extract source
    const sourceMatch = block.match(/SOURCE\s*:\s*([^\r\n]+)/i);
    if (sourceMatch) {
      let sourceLine = sourceMatch[1].trim();

      // Try to separate source from timestamp if they're in the same line
      const dateIndicators = [' - ', ' | ', ' at ', ' on ', ', '];

      for (const separator of dateIndicators) {
        if (sourceLine.includes(separator)) {
          const parts = sourceLine.split(separator);
          source = parts[0].trim();
          timestamp = parts.slice(1).join(separator).trim();
          break;
        }
      }

      // If no timestamp was separated, use the whole line as source
      if (!timestamp && sourceLine) {
        source = sourceLine;
      }
    } else if (sourceOnlyBlocks.length > 0 && i < sourceOnlyBlocks.length) {
      // If there are standalone source blocks, use them
      source = sourceOnlyBlocks[i].source;
      console.log(`Assigned standalone source to block ${i}: ${source}`);
    }

    // Extract impact
    const impactMatch = block.match(/IMPACT\s*:\s*(\w+)/i);
    if (impactMatch) {
      const impactValue = impactMatch[1].trim().toLowerCase();

      if (impactValue === 'high' || impactValue === 'medium' || impactValue === 'low') {
        impact = impactValue as 'high' | 'medium' | 'low';
        console.log(`Found impact rating in block ${i}: ${impact}`);
      }
    }

    // Extract URL - look for URL tag first
    const urlTagRegex = /URL\s*:\s*((?:https?:\/\/)?[^\s\r\n]+)/i;
    const urlTagMatch = block.match(urlTagRegex);

    console.log(`Searching for URL tag in block ${i}...`);
    if (urlTagMatch) {
      url = urlTagMatch[1].trim();
      // Clean up URL if it has trailing punctuation
      url = url.replace(/[.,;:)\]"']+$/, '');

      // Handle markdown links [text](url)
      if (url.includes('[') && url.includes('](')) {
        const markdownMatch = url.match(/\[([^\]]+)\]\(([^)]+)\)/);
        if (markdownMatch) {
          url = markdownMatch[2];
          console.log(`Fixed markdown link in URL tag: ${url}`);
        }
      }
      // Fix malformed URLs with square brackets (common in API responses)
      else if (url.includes('[') && url.includes(']')) {
        const bracketMatch = url.match(/\[(https?:\/\/[^\]]+)\]/);
        if (bracketMatch) {
          url = bracketMatch[1];
          console.log(`Fixed URL with square brackets: ${url}`);
        }
      }

      // Fix double URLs (another common issue from the API)
      if (url.includes('https://https://')) {
        url = url.replace('https://https://', 'https://');
        console.log(`Fixed double protocol: ${url}`);
      }

      // Fix URLs that contain another URL in them
      const embeddedUrlMatch = url.match(/https?:\/\/[^\/]+(https?:\/\/.*)/);
      if (embeddedUrlMatch) {
        url = embeddedUrlMatch[1];
        console.log(`Extracted embedded URL: ${url}`);
      }

      // Fix HTML entities
      url = url.replace(/&amp;/g, '&');

      // Ensure URL has http/https prefix
      if (!url.match(/^https?:\/\//i)) {
        url = 'https://' + url;
      }
      console.log(`Found URL tag in block ${i}: ${url}`);
    } else {
      console.log(`No URL tag found in block ${i}, searching for general URL...`);
      // Look for any URL in the text if no URL tag
      const generalUrlRegex = /(?:https?:\/\/)?[a-z0-9][-a-z0-9.]*\.[a-z]{2,}(?:\/[-\w./?&=+%#]*\S)?/i;
      const generalUrlMatch = block.match(generalUrlRegex);
      if (generalUrlMatch) {
        url = generalUrlMatch[0].trim();
        // Clean up URL
        url = url.replace(/[.,;:)\]"']+$/, '');

        // Fix malformed URLs with square brackets
        if (url.includes('[') && url.includes(']')) {
          const bracketMatch = url.match(/\[(https?:\/\/[^\]]+)\]/);
          if (bracketMatch) {
            url = bracketMatch[1];
            console.log(`Fixed general URL with square brackets: ${url}`);
          }
        }

        // Fix double URLs
        if (url.includes('https://https://')) {
          url = url.replace('https://https://', 'https://');
          console.log(`Fixed double protocol in general URL: ${url}`);
        }

        // Fix URLs that contain another URL in them
        const embeddedUrlMatch = url.match(/https?:\/\/[^\/]+(https?:\/\/.*)/);
        if (embeddedUrlMatch) {
          url = embeddedUrlMatch[1];
          console.log(`Extracted embedded URL from general URL: ${url}`);
        }

        // Fix HTML entities
        url = url.replace(/&amp;/g, '&');

        // Ensure URL has http/https prefix
        if (!url.match(/^https?:\/\//i)) {
          url = 'https://' + url;
        }
        console.log(`Found general URL in block ${i}: ${url}`);
      } else if (urlOnlyBlocks.length > 0 && i < urlOnlyBlocks.length) {
        // If there are standalone URLs, pair them with news items
        url = urlOnlyBlocks[i];
        console.log(`Assigned standalone URL to block ${i}: ${url}`);
      } else if (sourceOnlyBlocks.length > 0 && i < sourceOnlyBlocks.length && sourceOnlyBlocks[i].url) {
        // Check if the source block has a URL
        url = sourceOnlyBlocks[i].url || '';
        console.log(`Assigned URL from source block to item ${i}: ${url}`);
      } else {
        console.log(`No URL found for block ${i}`);
      }
    }

    // Final URL validation
    if (url) {
      // Fix URLs with markdown-style links [text](url) - one more check
      const markdownMatch = url.match(/\[([^\]]+)\]\(([^)]+)\)/);
      if (markdownMatch) {
        url = markdownMatch[2]; // Extract the URL part
        console.log(`Fixed markdown-style URL: ${url}`);
      }

      // Fix URL encoding issues
      url = url.replace(/&amp;/g, '&');

      try {
        // Try to create a URL object to validate
        new URL(url);
        console.log(`Valid URL for block ${i}: ${url}`);
      } catch (e) {
        console.error(`Invalid URL format for block ${i}: ${url}`, e);
        // Attempt to fix common issues
        if (!url.match(/^https?:\/\//i)) {
          url = 'https://' + url;
          console.log(`Fixed URL by adding https:// prefix: ${url}`);
          try {
            new URL(url); // Validate again
          } catch (e) {
            console.error(`Still invalid after fixing: ${url}`);
            // Try removing any remaining markdown or special characters
            url = url.replace(/[\[\]()]/g, '');
            try {
              new URL('https://' + url.replace(/^https?:\/\//i, ''));
              url = 'https://' + url.replace(/^https?:\/\//i, '');
              console.log(`Fixed URL by cleaning special chars: ${url}`);
            } catch (e) {
              console.error(`Still invalid after cleaning: ${url}`);
              url = ''; // Clear invalid URL
            }
          }
        } else {
          // For URLs that already have a protocol but are still invalid
          // Common case: URLs with spaces or other invalid characters
          try {
            url = encodeURI(url);
            new URL(url); // Try again with encoded URL
            console.log(`Fixed URL by encoding: ${url}`);
          } catch (e) {
            // Try removing any remaining markdown or special characters
            url = url.replace(/[\[\]()]/g, '');
            try {
              new URL(url);
              console.log(`Fixed URL by cleaning special chars: ${url}`);
            } catch (e) {
              console.error(`Still invalid after encoding and cleaning: ${url}`);
              url = ''; // Clear invalid URL
            }
          }
        }
      }
    }

    // Extract content - exclude lines that are clearly metadata
    const contentLines = lines.filter(line =>
      !line.match(/^(?:HEADLINE|SOURCE|IMPACT|URL)\s*:/i) &&
      !line.match(/^https?:\/\//i) &&
      line !== title
    );

    // Join the content lines
    content = contentLines.join('\n').trim();

    // If we still don't have a good content extraction, use the whole block
    if (!content || content.length < 20) {
      content = block;
    }

    // Clean up markdown in content
    content = content.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '$1');
    content = content.replace(/\*\*([^*]+)\*\*/g, '$1'); // Remove bold markdown
    content = content.replace(/\*([^*]+)\*/g, '$1'); // Remove italic markdown

    // Check if we need to clean up source further (sometimes contains timestamp)
    if (source && source.match(/\d{1,2}:\d{2}/)) {
      const timeMatch = source.match(/\d{1,2}:\d{2}/);
      if (timeMatch) {
        const timeIndex = source.indexOf(timeMatch[0]);
        timestamp = source.substring(timeIndex).trim();
        source = source.substring(0, timeIndex).trim();
      }
    }

    // Final cleanup to remove URLs from source
    if (source) {
      source = source.replace(/https?:\/\/[^\s]+/g, '').trim();
    }

    // Clean up content - remove any standalone URLs that might be in the content
    content = content.replace(/^(?:URL\s*:\s*)?https?:\/\/[^\s\r\n]+$/gim, '').trim();

    // Remove title from content if it appears at the beginning
    if (content.startsWith(title)) {
      content = content.substring(title.length).trim();
    }

    // Debug: Log extracted components
    console.log(`===== EXTRACTED COMPONENTS FOR BLOCK ${i} =====`);
    console.log(`Title: ${title}`);
    console.log(`Source: ${source}`);
    console.log(`Timestamp: ${timestamp}`);
    console.log(`Impact: ${impact}`);
    console.log(`URL: ${url}`);
    console.log(`Content snippet: ${content.substring(0, 50)}...`);

    // Skip blocks that are only URLs or sources
    const isTitleUrlOrSource = title.match(/^(?:URL|SOURCE|IMPACT)\s*:/i) ||
                              title.match(/^https?:\/\//i);

    // Only push news items that have a valid title
    if (title && title.length > 3 && !isTitleUrlOrSource) {
      newsItems.push({
        title: title || 'Market Update',
        content,
        source,
        timestamp,
        impact,
        url
      });
    } else {
      console.log(`Skipping block ${i} due to missing/invalid title or being just a URL/source`);
    }
  }

  // Debug: Log final parsed news items
  console.log("===== FINAL PARSED NEWS ITEMS =====");
  newsItems.forEach((item, index) => {
    console.log(`Item ${index}:`);
    console.log(`- Title: ${item.title.substring(0, 30)}...`);
    console.log(`- Source: ${item.source}`);
    console.log(`- Impact: ${item.impact}`);
    console.log(`- Has URL: ${!!item.url}`);
    if (item.url) console.log(`- URL: ${item.url}`);
  });

  return newsItems;
};

/**
 * Parses the Perplexity API market prediction response
 */
export const parseMarketPrediction = (response: PerplexityResponse): MarketPrediction => {
  if (!response?.choices?.length) {
    throw new Error('Invalid API response');
  }

  const content = response.choices[0].message.content.trim();

  // Debug: Print entire API response for troubleshooting
  console.log("===== FULL MARKET PREDICTION API RESPONSE START =====");
  console.log(content);
  console.log("===== FULL MARKET PREDICTION API RESPONSE END =====");

  // Initialize with default values
  const prediction: MarketPrediction = {
    prediction: 'Neutral',
    expectedOpen: 'Unchanged',
    confidence: 'Medium',
    analysis: '',
    keyFactors: [],
    risks: [],
    raw: content
  };

  // Try to parse as JSON first
  try {
    // Check if the content is a valid JSON string
    if (content.trim().startsWith('{') && content.trim().endsWith('}')) {
      const jsonData = JSON.parse(content);
      console.log("Successfully parsed JSON response:", jsonData);

      // Map JSON fields to our MarketPrediction interface
      if (jsonData.prediction) {
        const predValue = jsonData.prediction.trim().toLowerCase();
        if (predValue === 'bullish') {
          prediction.prediction = 'Bullish';
        } else if (predValue === 'bearish') {
          prediction.prediction = 'Bearish';
        } else {
          prediction.prediction = 'Neutral';
        }
      }

      if (jsonData.expected_spy_open) {
        prediction.expectedOpen = jsonData.expected_spy_open;
      }

      if (jsonData.confidence) {
        const confValue = jsonData.confidence.trim().toLowerCase();
        if (confValue === 'high') {
          prediction.confidence = 'High';
        } else if (confValue === 'low') {
          prediction.confidence = 'Low';
        } else {
          prediction.confidence = 'Medium';
        }
      }

      if (jsonData.analysis) {
        prediction.analysis = jsonData.analysis;
      }

      if (Array.isArray(jsonData.key_factors)) {
        prediction.keyFactors = jsonData.key_factors;
      }

      if (Array.isArray(jsonData.risks)) {
        prediction.risks = jsonData.risks;
      }

      return prediction;
    }
  } catch (error) {
    console.warn("Failed to parse JSON response, falling back to regex parsing:", error);
  }

  // Fallback to regex parsing if JSON parsing fails
  // Extract prediction
  const predictionMatch = content.match(/PREDICTION\s*:\s*(\w+)/i);
  if (predictionMatch) {
    const predictionValue = predictionMatch[1].trim().toLowerCase();
    if (predictionValue === 'bullish') {
      prediction.prediction = 'Bullish';
    } else if (predictionValue === 'bearish') {
      prediction.prediction = 'Bearish';
    } else {
      prediction.prediction = 'Neutral';
    }
  }

  // Extract expected open
  const openMatch = content.match(/EXPECTED\s+SPY\s+OPEN\s*:\s*([^\r\n]+)/i);
  if (openMatch) {
    prediction.expectedOpen = openMatch[1].trim();
  }

  // Extract confidence
  const confidenceMatch = content.match(/CONFIDENCE\s*:\s*(\w+)/i);
  if (confidenceMatch) {
    const confidenceValue = confidenceMatch[1].trim().toLowerCase();
    if (confidenceValue === 'high') {
      prediction.confidence = 'High';
    } else if (confidenceValue === 'low') {
      prediction.confidence = 'Low';
    } else {
      prediction.confidence = 'Medium';
    }
  }

  // Extract analysis
  const analysisMatch = content.match(/ANALYSIS\s*:\s*([^\r\n](?:.|[\r\n])*?)(?:KEY FACTORS|RISKS|$)/is);
  if (analysisMatch) {
    prediction.analysis = analysisMatch[1].trim();
  }

  // Extract key factors
  const keyFactorsMatch = content.match(/KEY FACTORS\s*:([^\r\n](?:.|[\r\n])*?)(?:RISKS|$)/is);
  if (keyFactorsMatch) {
    const factorsText = keyFactorsMatch[1].trim();
    prediction.keyFactors = factorsText
      .split(/\n\s*-\s*/)
      .map(item => item.trim())
      .filter(item => item.length > 0)
      .map(item => item.replace(/^-\s*/, '')); // Remove leading dash if present
  }

  // Extract risks
  const risksMatch = content.match(/RISKS[^:]*:\s*([^\r\n](?:.|[\r\n])*?)$/is);
  if (risksMatch) {
    const risksText = risksMatch[1].trim();
    prediction.risks = risksText
      .split(/\n\s*-\s*/)
      .map(item => item.trim())
      .filter(item => item.length > 0)
      .map(item => item.replace(/^-\s*/, '')); // Remove leading dash if present
  }

  return prediction;
};

export default parseNewsFromResponse;