import axios from 'axios';

export interface CatalystEvent {
  date: string;
  title: string;
  description: string;
  impact: 'High' | 'Medium' | 'Low';
  category: string;
}

class CatalystService {
  private cache: { [key: string]: { data: any; timestamp: number } } = {};
  private readonly CACHE_DURATION = 30 * 60 * 1000; // 30 minutes
  private loadingStates: { [key: string]: boolean } = {};

  private isDataCached(key: string): boolean {
    const cached = this.cache[key];
    if (!cached) return false;
    
    const isExpired = Date.now() - cached.timestamp > this.CACHE_DURATION;
    if (isExpired) {
      delete this.cache[key];
      return false;
    }
    
    return true;
  }

  private getCachedData(key: string): any {
    return this.cache[key]?.data;
  }

  private setCachedData(key: string, data: any): void {
    this.cache[key] = {
      data,
      timestamp: Date.now()
    };
  }

  public async getUpcomingCatalysts(days: number = 14): Promise<CatalystEvent[]> {
    const cacheKey = `catalysts-sonar-${days}`;
    
    // Check if already loading this request
    if (this.loadingStates[cacheKey]) {
      console.log('Catalyst request already in progress, waiting...');
      // Wait for the existing request to complete
      while (this.loadingStates[cacheKey]) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      // Return cached data if available
      if (this.isDataCached(cacheKey)) {
        return this.getCachedData(cacheKey);
      }
    }
    
    if (this.isDataCached(cacheKey)) {
      return this.getCachedData(cacheKey);
    }

    // Set loading state
    this.loadingStates[cacheKey] = true;

    try {
      console.log('Fetching upcoming market catalysts using Sonar Pro...');
      
      const apiKey = import.meta.env.VITE_PERPLEXITY_API_KEY;
      if (!apiKey) {
        throw new Error('Perplexity API key not found. Please add VITE_PERPLEXITY_API_KEY to your .env file.');
      }

      const lambdaUrl = 'https://yqhtu7exlgaxtlrrrahaykfgnm0qqdvl.lambda-url.us-east-1.on.aws/';
      
      const prompt = `You are a financial analyst. Provide upcoming market catalysts for the next ${days} days that could significantly impact the S&P 500 (SPY).

Focus on:
- Federal Reserve meetings and announcements
- Major economic data releases (CPI, PPI, employment data, GDP)
- Earnings releases from major S&P 500 companies
- Geopolitical events with market impact
- Central bank meetings globally

For each catalyst, provide:
1. Date (YYYY-MM-DD format)
2. Event name
3. Brief description
4. Expected market impact (High/Medium/Low)

Format as JSON array with this structure:
[
  {
    "date": "2024-01-15",
    "title": "Federal Reserve Meeting",
    "description": "FOMC meeting with potential rate decision",
    "impact": "High",
    "category": "Monetary Policy"
  }
]

Only return the JSON array, no additional text.`;

      const response = await axios.post(lambdaUrl, {
        model: 'llama-3.1-sonar-large-128k-online',
        messages: [
          {
            role: 'system',
            content: 'You are a financial analyst providing market catalyst information. Always respond with valid JSON only.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 2000,
        temperature: 0.1
      }, {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      });

      let catalysts: CatalystEvent[] = [];
      
      if (response.data && response.data.choices && response.data.choices[0]) {
        const content = response.data.choices[0].message.content;
        
        try {
          const jsonMatch = content.match(/\[[\s\S]*\]/);
          if (jsonMatch) {
            const parsedCatalysts = JSON.parse(jsonMatch[0]);
            
            catalysts = parsedCatalysts.map((catalyst: any) => ({
              date: catalyst.date,
              title: catalyst.title || catalyst.event || 'Market Event',
              description: catalyst.description || '',
              impact: catalyst.impact || 'Medium',
              category: catalyst.category || 'Economic Data'
            }));
          }
        } catch (parseError) {
          console.error('Error parsing catalyst JSON:', parseError);
          console.log('Raw content:', content);
        }
      }

      // Sort by date
      const sortedCatalysts = catalysts.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
      
      this.setCachedData(cacheKey, sortedCatalysts);
      console.log(`Successfully parsed ${sortedCatalysts.length} catalysts`);
      return sortedCatalysts;
    } catch (error) {
      console.error('Error fetching catalysts using Sonar Pro:', error);
      throw error;
    } finally {
      // Clear loading state
      this.loadingStates[cacheKey] = false;
    }
  }

  public async getAllUpcomingCatalysts(): Promise<CatalystEvent[]> {
    return this.getUpcomingCatalysts(30);
  }

  public async getNextMajorCatalyst(): Promise<CatalystEvent | null> {
    try {
      const catalysts = await this.getAllUpcomingCatalysts();
      const highImpactCatalysts = catalysts.filter(c => c.impact === 'High');
      return highImpactCatalysts.length > 0 ? highImpactCatalysts[0] : catalysts[0] || null;
    } catch (error) {
      console.error('Error fetching next major catalyst:', error);
      throw error;
    }
  }

  public async getCatalystSummary(catalysts: CatalystEvent[]): Promise<string> {
    const cacheKey = 'catalyst-summary';
    
    if (this.isDataCached(cacheKey)) {
      return this.getCachedData(cacheKey);
    }

    try {
      const apiKey = import.meta.env.VITE_PERPLEXITY_API_KEY;
      if (!apiKey) {
        throw new Error('Perplexity API key not found');
      }

      const catalystText = catalysts.map(c => 
        `${c.date}: ${c.title} (${c.impact} impact) - ${c.description}`
      ).join('\n');

      const prompt = `Analyze these upcoming market catalysts and provide a concise summary of their potential combined impact on the S&P 500 (SPY):

${catalystText}

Provide:
1. Overall market outlook based on these catalysts
2. Key dates to watch
3. Potential volatility periods
4. Strategic considerations for SPY traders

Keep the response concise and actionable.`;

      const response = await axios.post('https://api.perplexity.ai/chat/completions', {
        model: 'llama-3.1-sonar-large-128k-online',
        messages: [
          {
            role: 'system',
            content: 'You are a financial analyst specializing in market catalyst analysis and SPY trading strategies.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1000,
        temperature: 0.1
      }, {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      const summary = response.data.choices[0].message.content;
      this.setCachedData(cacheKey, summary);
      return summary;
    } catch (error) {
      console.error('Error generating catalyst summary:', error);
      throw error;
    }
  }

  public clearCache(): void {
    this.cache = {};
    this.loadingStates = {};
  }
}

export const catalystService = new CatalystService();
