import { MarketData } from './marketData';

// Type for trade data received from Finnhub
interface FinnhubTradeData {
  p: number;  // Last price
  s: string;  // Symbol
  t: number;  // UNIX milliseconds timestamp
  v: number;  // Volume
  c?: string[];  // Trade conditions
}

// Type for the WebSocket message
interface FinnhubMessage {
  type: string;
  data?: FinnhubTradeData[];
}

// Type for quote data from Finnhub REST API
interface FinnhubQuoteResponse {
  c: number;   // Current price
  d: number;   // Change
  dp: number;  // Percent change
  h: number;   // High price of the day
  l: number;   // Low price of the day
  o: number;   // Open price of the day
  pc: number;  // Previous close price
  t: number;   // Timestamp
}

class FinnhubService {
  private static instance: FinnhubService;
  private socket: WebSocket | null = null;
  // We're not using the API key directly anymore, but through the Lambda proxy
  private apiKey: string = ''; // Removed direct API key dependency
  private useFallbackData: boolean = false;
  private lastData: MarketData | null = null;
  private listeners: ((data: MarketData) => void)[] = [];
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 2000; // Start with 2 seconds
  private previousDayClose: number | null = null;
  private isFetchingPreviousClose = false;
  private pendingTradeData: FinnhubTradeData[] = [];
  private firstTradePrice: number | null = null;
  private fetchCloseTimeoutId: number | null = null;

  private constructor() {
    // Private constructor to enforce singleton
    // Set a timeout to fallback if API is slow
    this.fetchCloseTimeoutId = window.setTimeout(() => {
      if (this.previousDayClose === null) {
        console.warn('Timeout reached waiting for previous day close. Using fallback approach.');
        this.useFallbackPriceReference();
      }
    }, 5000); // 5 second timeout

    this.fetchPreviousDayClose().then(() => {
      // Process any pending trade data after we have the previous day's close
      this.processPendingTradeData();
    });

    this.initSocket();
  }

  public static getInstance(): FinnhubService {
    if (!FinnhubService.instance) {
      FinnhubService.instance = new FinnhubService();
    }
    return FinnhubService.instance;
  }

  private useFallbackPriceReference(): void {
    // If we have a first trade price, use that temporarily
    if (this.firstTradePrice !== null) {
      console.log(`Using first received trade price as temporary reference: $${this.firstTradePrice.toFixed(2)}`);
      this.previousDayClose = this.firstTradePrice;
      this.processPendingTradeData();
    }
  }

  private async fetchPreviousDayClose(): Promise<void> {
    if (!this.apiKey || this.isFetchingPreviousClose) {
      return;
    }

    this.isFetchingPreviousClose = true;
    
    try {
      // Use Lambda proxy to avoid API key issues
      const lambdaUrl = import.meta.env.VITE_LAMBDA_FUNCTION_URL;
      if (!lambdaUrl) {
        throw new Error('Lambda function URL is missing. Please add VITE_LAMBDA_FUNCTION_URL to your environment variables.');
      }
      
      const response = await fetch(lambdaUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'finnhubRequest',
          endpoint: 'quote',
          method: 'get',
          params: { symbol: 'SPY' }
        })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch quote: ${response.status} ${response.statusText}`);
      }
      
      const data: FinnhubQuoteResponse = await response.json();
      
      // Use the pc (previous close) value directly from the quote endpoint
      if (data && data.pc) {
        this.previousDayClose = data.pc;
        console.log(`Previous day close for SPY: $${this.previousDayClose.toFixed(2)}`);
        
        // Clear any pending timeout
        if (this.fetchCloseTimeoutId !== null) {
          window.clearTimeout(this.fetchCloseTimeoutId);
          this.fetchCloseTimeoutId = null;
        }
      } else {
        console.warn('No previous close data available from Finnhub Quote API');
        this.useFallbackPriceReference();
      }
    } catch (error) {
      console.error('Error fetching previous day close:', error);
      this.useFallbackPriceReference();
    } finally {
      this.isFetchingPreviousClose = false;
    }
  }

  private processPendingTradeData(): void {
    if (this.previousDayClose !== null && this.pendingTradeData.length > 0) {
      console.log(`Processing ${this.pendingTradeData.length} pending trade updates`);
      
      // Process all pending trade data
      for (const tradeData of this.pendingTradeData) {
        this.processTradeData(tradeData);
      }
      
      // Clear the pending queue
      this.pendingTradeData = [];
    }
  }

  private initSocket(): void {
    // Check for Lambda URL instead of API key
    const lambdaUrl = import.meta.env.VITE_LAMBDA_FUNCTION_URL;
    if (!lambdaUrl) {
      console.error('Lambda function URL is missing. Please add VITE_LAMBDA_FUNCTION_URL to your environment variables.');
      this.useFallbackMode();
      return;
    }

    try {
      // Get the Finnhub API key from the environment
      const apiKey = import.meta.env.VITE_FINNHUB_API_KEY;
      if (!apiKey) {
        console.error('Finnhub API key is missing. Please add VITE_FINNHUB_API_KEY to your environment variables.');
        this.useFallbackMode();
        return;
      }
      
      // Connect to Finnhub WebSocket API
      this.socket = new WebSocket(`wss://ws.finnhub.io?token=${apiKey}`);
      
      // Connection opened -> Subscribe
      this.socket.addEventListener('open', (event) => {
        console.log('Finnhub WebSocket connection opened');
        this.reconnectAttempts = 0;
        
        // Subscribe to SPY trade data exactly as in the documentation
        if (this.socket) {
          this.socket.send(JSON.stringify({'type':'subscribe', 'symbol': 'SPY'}));
        }
      });
      
      // Listen for messages
      this.socket.addEventListener('message', (event) => {
        try {
          const message = JSON.parse(event.data);
          
          if (message.type === 'trade' && message.data && message.data.length > 0) {
            const spyData = message.data.find((trade: FinnhubTradeData) => trade.s === 'SPY');
            if (spyData) {
              // Store the first trade price as a potential fallback
              if (this.firstTradePrice === null) {
                this.firstTradePrice = spyData.p;
              }
              
              if (this.previousDayClose !== null) {
                this.processTradeData(spyData);
              } else {
                // Queue the trade data to be processed once we have the previous day's close
                this.pendingTradeData.push(spyData);
                
                // If we're not currently fetching, try to fetch the previous day's close
                if (!this.isFetchingPreviousClose) {
                  this.fetchPreviousDayClose().then(() => {
                    this.processPendingTradeData();
                  });
                }
              }
            }
          }
        } catch (error) {
          console.error('Error processing WebSocket message:', error);
        }
      });
      
      // Handle errors
      this.socket.addEventListener('error', (event) => {
        console.error('Finnhub WebSocket error:', event);
        // If we get an error, switch to fallback mode
        this.useFallbackMode();
      });
      
      // Handle connection close
      this.socket.addEventListener('close', (event) => {
        console.log('Finnhub WebSocket connection closed', event.code, event.reason);
        
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnectAttempts++;
          const delay = Math.min(30000, this.reconnectDelay * this.reconnectAttempts);
          console.log(`Attempting to reconnect in ${delay / 1000} seconds...`);
          
          setTimeout(() => {
            console.log('Reconnecting...');
            this.initSocket();
          }, delay);
        } else {
          console.error('Maximum reconnect attempts reached');
          this.useFallbackMode();
        }
      });
      
      console.log('Finnhub WebSocket initialized');
    } catch (error) {
      console.error('Error initializing Finnhub WebSocket:', error);
      this.useFallbackMode();
    }
  }

  private processTradeData(tradeData: FinnhubTradeData): void {
    if (this.previousDayClose === null) {
      console.warn('Cannot process trade data: previous day close is not available');
      return;
    }
    
    const price = tradeData.p;
    const change = price - this.previousDayClose;
    const changePercent = (change / this.previousDayClose) * 100;
    
    const marketData: MarketData = {
      price,
      change,
      changePercent,
      volume: tradeData.v,
      lastUpdate: new Date(tradeData.t).toLocaleTimeString()
    };
    
    this.lastData = marketData;
    
    // Notify all listeners
    this.listeners.forEach(listener => listener(marketData));
    
    // Comment out to reduce console noise during debugging
    // console.log('Real-time SPY data updated:', marketData);
  }

  public addListener(callback: (data: MarketData) => void): void {
    this.listeners.push(callback);
    
    // If we have data already, send it immediately
    if (this.lastData) {
      callback(this.lastData);
    }
  }

  public removeListener(callback: (data: MarketData) => void): void {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  public getLastData(): MarketData | null {
    return this.lastData;
  }
  
  public disconnect(): void {
    // If we're in fallback mode, just return
    if (this.useFallbackData) {
      return;
    }
    
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      // Unsubscribe before disconnecting (per Finnhub docs)
      this.socket.send(JSON.stringify({'type':'unsubscribe','symbol': 'SPY'}));
      this.socket.close();
      this.socket = null;
      console.log('Finnhub WebSocket disconnected');
    }
  }
  
  public async refresh(): Promise<void> {
    // Reset fallback mode
    this.useFallbackData = false;
    
    // Disconnect existing socket if any
    this.disconnect();
    
    // Refetch previous day close
    await this.fetchPreviousDayClose();
    
    // Reinitialize socket
    this.initSocket();
  }
  
  // Fallback mode when WebSocket fails
  private useFallbackMode(): void {
    if (this.useFallbackData) {
      return; // Already in fallback mode
    }
    
    console.log('Switching to fallback market data mode');
    this.useFallbackData = true;
    
    // Create some reasonable fallback data
    const fallbackData: MarketData = {
      price: 620.45, // Example price
      change: -1.27,
      changePercent: -0.2,
      volume: 50000000,
      lastUpdate: new Date().toLocaleTimeString()
    };
    
    // Update the last data
    this.lastData = fallbackData;
    
    // Notify all listeners
    this.listeners.forEach(listener => listener(fallbackData));
    
    // Set up a timer to simulate periodic updates
    setInterval(() => {
      if (!this.useFallbackData) return;
      
      // Generate slightly different data each time
      const randomChange = (Math.random() * 2 - 1) * 0.5; // Random change between -0.5 and +0.5
      const lastPrice = this.lastData?.price || 620.45;
      const newPrice = Math.round((lastPrice + randomChange) * 100) / 100;
      
      const newData: MarketData = {
        price: newPrice,
        change: Math.round((newPrice - 621.72) * 100) / 100, // Relative to fixed previous close
        changePercent: Math.round(((newPrice - 621.72) / 621.72 * 100) * 100) / 100,
        volume: Math.floor(50000000 + Math.random() * 10000000),
        lastUpdate: new Date().toLocaleTimeString()
      };
      
      // Update the last data
      this.lastData = newData;
      
      // Notify all listeners
      this.listeners.forEach(listener => listener(newData));
    }, 10000); // Update every 10 seconds
  }
}

// Create and export the singleton instance
const finnhubService = FinnhubService.getInstance();
export default finnhubService;