import axios from 'axios';

// Define types
export interface PerplexityResponse {
  id: string;
  model: string;
  choices: {
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Default search query for S&P 500 relevant news
const defaultQuery =
  `Find at least 10 of the most important news from TODAY (${new Date().toLocaleDateString()}) that could impact the S&P 500 (SPY).
   Focus on market-moving events, economic indicators, Fed statements, geopolitical risks,
   and significant corporate developments. Include timestamps and sources.

   IMPORTANT: STRONGLY PRIORITIZE news from TODAY (${new Date().toLocaleDateString()}). Only include yesterday's news if there are not enough significant market-moving stories from today.

   IMPORTANT: For each news item, include the EXACT DATE in the source line (e.g., "April 4, 2024" or "4/4/2024"), not just "Today" or "Yesterday".

   IMPORTANT: Format your response with each news item following this EXACT structure:

   HEADLINE: [Clear and concise headline]
   SOURCE: [Source name] - [Exact date and time, e.g., April 4, 2024 at 2:15 PM ET]
   IMPACT: [high|medium|low] (evaluate how impactful this news is to the S&P 500)
   [Brief summary of the news item (2-3 sentences)]
   URL: [Full clickable URL link to the source]

   [blank line between news items]

   Example:
   HEADLINE: Fed Signals Rate Cut
   SOURCE: Wall Street Journal - April 4, 2024 at 2:15 PM ET
   IMPACT: high
   The Federal Reserve indicated it may cut interest rates in its next meeting based on new inflation data. Chair Powell mentioned that recent economic indicators suggest cooling inflation.
   URL: https://www.wsj.com/articles/fed-signals-possible-rate-cut-283746

   HEADLINE: Next news item...`;

// API service for fetching news using Perplexity's Sonar model
const apiService = {
  generateOptionsStrategy: async (marketPrediction: string, newsContext: string, catalystData?: string): Promise<PerplexityResponse> => {
    // Check for Lambda URL instead of API key
    const lambdaUrl = import.meta.env.VITE_LAMBDA_FUNCTION_URL;
    if (!lambdaUrl) {
      throw new Error('Lambda function URL is missing. Please add VITE_LAMBDA_FUNCTION_URL to your environment variables.');
    }

    const prompt = `Based on the following market prediction, news items, and upcoming market catalysts, generate a detailed high-probability options strategy for SPY for the next trading day. Instead of exact numbers that may be incorrect or outdated, use relative measures and ranges to ensure the strategy remains valid regardless of exact price levels.

MARKET PREDICTION:
${marketPrediction}

NEWS CONTEXT:
${newsContext}

${catalystData ? `UPCOMING MARKET CATALYSTS:
${catalystData}

IMPORTANT: Consider how these upcoming catalysts might affect market volatility, timing of the strategy, and position sizing. Factor in any earnings announcements, FOMC meetings, or major economic data releases when recommending expiration dates and strike selection.` : ''}

IMPORTANT: DO NOT include any markdown formatting in your response. Return a valid JSON object with the following structure. Do not include any text outside the JSON object:

{
  "strategy_name": "Specific name of strategy, e.g. Bullish Call Spread or Bearish Put Spread",
  "overview": "1-2 detailed paragraphs explaining the strategy, why it's appropriate given the market prediction, and expected outcome",
  "entry_time": "Specific guidance for entry, e.g. Market open or After first hour of trading",
  "expiration_timeframe": "Specific timeframe, e.g. Short-term (7-14 days) or Medium-term (30-45 days)",
  "strike_selection": "Specific guidance like ATM and 2% OTM calls or 5% OTM put and 10% OTM put",
  "implied_volatility": "Specific assessment of current IV, e.g. High (above historical average)",
  "position_size": "Specific recommendation relative to portfolio, e.g. Small (1-2% of portfolio)",
  "risk_reward": "Specific ratio, e.g. 2:1 potential reward to risk",
  "profit_potential": "Specific description in relative terms, e.g. Up to 35% return on capital at max profit",
  "max_loss": "Specific description in relative terms, e.g. Limited to net premium paid",
  "break_even": "Specific relative description, e.g. Approximately strike price plus premium paid",
  "current_trend": "Specific description of market trend, e.g. Short-term downtrend, longer-term uptrend",
  "volatility_environment": "Specific description of volatility conditions, e.g. Elevated and increasing",
  "sentiment": "Specific description of market sentiment, e.g. Cautiously bullish with concerns about upcoming economic data",
  "stop_loss": "Specific guidance on exit criteria, e.g. Exit if SPY breaks below support at recent low",
  "take_profit": "Specific profit target guidance, e.g. Consider taking profits at 50-75% of maximum potential profit",
  "adjustment_criteria": "Specific conditions that would trigger position adjustments, e.g. If SPY falls below short put strike, consider rolling down and out",
  "related_stocks": [
    {
      "symbol": "Specific ticker symbol",
      "recommendation": "Buy/Sell/Hold",
      "rationale": "Clear explanation of relevance to strategy",
      "outlook": "Specific directional outlook, e.g. Likely to outperform SPY if tech sector leads the rally"
    },
    {
      "symbol": "Specific ticker symbol",
      "recommendation": "Buy/Sell/Hold",
      "rationale": "Clear explanation of relevance to strategy",
      "outlook": "Specific directional outlook"
    },
    {
      "symbol": "Specific ticker symbol",
      "recommendation": "Buy/Sell/Hold",
      "rationale": "Clear explanation of relevance to strategy",
      "outlook": "Specific directional outlook"
    }
  ]
}

You MUST fill in all fields with detailed, specific information. Vague or generic responses are not acceptable. Your strategy should be data-driven, practical, and comprehensive. DO NOT LEAVE ANY FIELD BLANK or use placeholder text. Ensure the response is a valid JSON object that can be parsed with JSON.parse().`;

    const requestData = {
      model: "sonar-pro",
      messages: [
        {
          role: "system",
          content: "You are a professional options trader and market analyst specializing in S&P 500 options strategies. You provide detailed, practical options strategies based on market predictions and news analysis. Instead of exact numbers that might be inaccurate, you focus on relative measures (like 'ATM calls' rather than exact strike prices) and ranges that remain valid regardless of exact price levels. You explain option concepts clearly, providing educational context along with strategic recommendations. Your guidance includes entry timing, position sizing, expiration timeframes, and risk management principles. Your strategies are adaptable, educational, and include clear rationale for why they're appropriate given current market conditions. IMPORTANT: You MUST fill in ALL requested fields with specific, detailed information. Do not leave any fields blank or use placeholder text. Be precise and comprehensive in all your responses. CRITICAL: Return your response as a valid JSON object with no markdown formatting or text outside the JSON structure. The response must be parseable by JSON.parse()."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 32000,
      temperature: 0.1,
      web_search_options: {
        "search_context_size": "high",
        "recency_bias": "strong"
      },
    };

    try {
      console.log("Generating options strategy...");
      console.log("Full request to Sonarn:", JSON.stringify(requestData, null, 2));

      // Use Lambda proxy to avoid CORS issues
      const lambdaUrl = import.meta.env.VITE_LAMBDA_FUNCTION_URL;
      if (!lambdaUrl) {
        throw new Error('Lambda function URL is missing. Please add VITE_LAMBDA_FUNCTION_URL to your environment variables.');
      }
      
      // Use a CORS proxy to avoid CORS issues
      const corsProxyUrl = 'https://cors-anywhere.herokuapp.com/';
      const response = await axios.post(corsProxyUrl + lambdaUrl, {
        action: 'perplexityRequest',
        requestData
      }, {
        headers: {
          'Origin': window.location.origin
        }
      });

      if (response.status !== 200) {
        console.error("API Response:", response.data);
        throw new Error(`API error ${response.status}: ${JSON.stringify(response.data)}`);
      }

      const responseData = response.data;
      console.log("Raw API response:", JSON.stringify(responseData, null, 2));

      return responseData;
    } catch (error) {
      console.error('Error generating options strategy:', error);
      throw error;
    }
  },
  fetchMarketNews: async (query: string = defaultQuery): Promise<PerplexityResponse> => {
    // Check for Lambda URL instead of API key
    const lambdaUrl = import.meta.env.VITE_LAMBDA_FUNCTION_URL;
    if (!lambdaUrl) {
      throw new Error('Lambda function URL is missing. Please add VITE_LAMBDA_FUNCTION_URL to your environment variables.');
    }

    // Ensure the query is never empty
    const safeQuery = query && query.trim() ? query.trim() : defaultQuery;

    // Format instructions for any custom query
    const formattedQuery = safeQuery === defaultQuery
      ? safeQuery
      : `${safeQuery}

         IMPORTANT: STRONGLY PRIORITIZE news from TODAY (${new Date().toLocaleDateString()}). Only include yesterday's news if there are not enough significant market-moving stories from today.

         IMPORTANT: For each news item, include the EXACT DATE in the source line (e.g., "April 4, 2024" or "4/4/2024"), not just "Today" or "Yesterday".

         IMPORTANT: Format your response with each news item following this EXACT structure:

         HEADLINE: [Clear headline]
         SOURCE: [Source name] - [Exact date and time, e.g., April 4, 2024 at 2:15 PM ET]
         IMPACT: [high|medium|low] (evaluate how impactful this news is to the S&P 500)
         [Brief summary of the news (2-3 sentences)]
         URL: [Full clickable URL link to the source]

         [blank line between news items]`;

    // Using a more minimal, robust request format
    const requestData = {
      model: "sonar-pro",
      messages: [
        {
          role: "system",
          content: `You are a financial news analyst specialized in finding market-moving news that impacts the S&P 500 index and SPY ETF. Focus on actionable, time-sensitive information from TODAY (${new Date().toLocaleDateString()}) FIRST AND FOREMOST. Be precise, factual, and concise. STRONGLY PRIORITIZE TODAY'S NEWS over yesterday's news. For each news item, evaluate its potential impact on the S&P 500 (high, medium, or low) based on how likely it is to move the market. ALWAYS follow the requested format EXACTLY with HEADLINE, SOURCE, IMPACT, content, and URL fields separated on their own lines. Make sure all links are properly formatted and clickable. Always provide full, complete URLs with the EXACT DATE (e.g., 'April 4, 2024') in the source line. Give at least 10 articles, with as many as possible from TODAY. DO NOT use any markdown formatting in your response.`
        },
        {
          role: "user",
          content: formattedQuery
        }
      ],
      max_tokens: 32000,
      temperature: 0.1,
      web_search_options: {
        "search_context_size": "high",
        "recency_bias": "strong"
      },
    };

    try {
      console.log("Sending API request with query:", safeQuery.substring(0, 50) + "...");
      console.log("Full request to Sonarn:", JSON.stringify(requestData, null, 2));

      // Use Lambda proxy to avoid CORS issues
      const lambdaUrl = import.meta.env.VITE_LAMBDA_FUNCTION_URL;
      if (!lambdaUrl) {
        throw new Error('Lambda function URL is missing. Please add VITE_LAMBDA_FUNCTION_URL to your environment variables.');
      }
      
      const response = await axios.post(lambdaUrl, {
        action: 'perplexityRequest',
        requestData
      });

      if (response.status !== 200) {
        console.error("API Response:", response.data);
        throw new Error(`API error ${response.status}: ${JSON.stringify(response.data)}`);
      }

      const responseData = response.data;
      console.log("Raw API response:", JSON.stringify(responseData, null, 2));

      return responseData;
    } catch (error) {
      console.error('Error fetching market news:', error);
      throw error;
    }
  },

  askFollowUpQuestion: async (question: string, context: string): Promise<PerplexityResponse> => {
    // Check for Lambda URL instead of API key
    const lambdaUrl = import.meta.env.VITE_LAMBDA_FUNCTION_URL;
    if (!lambdaUrl) {
      throw new Error('Lambda function URL is missing. Please add VITE_LAMBDA_FUNCTION_URL to your environment variables.');
    }

    const requestData = {
      model: "sonar-pro",
      messages: [
        {
          role: "system",
          content: "You are a financial market analyst specialized in analyzing S&P 500 market movements. You provide clear, expert answers to financial questions based on market news. Answer concisely but with enough detail to be helpful, maintaining a professional tone. DO NOT use any markdown formatting in your response. Provide plain text only."
        },
        {
          role: "user",
          content: `Based on the following market news and analysis, please answer this question: ${question}

CONTEXT:
${context}`
        }
      ],
      max_tokens: 32000,
      temperature: 0.1,
      web_search_options: {
        "search_context_size": "high",
        "recency_bias": "strong"
      },
    };

    try {
      console.log("Sending follow-up question:", question);
      console.log("Full request to Sonarn:", JSON.stringify(requestData, null, 2));

      // Use Lambda proxy to avoid CORS issues
      const lambdaUrl = import.meta.env.VITE_LAMBDA_FUNCTION_URL;
      if (!lambdaUrl) {
        throw new Error('Lambda function URL is missing. Please add VITE_LAMBDA_FUNCTION_URL to your environment variables.');
      }
      
      const response = await axios.post(lambdaUrl, {
        action: 'perplexityRequest',
        requestData
      });

      if (response.status !== 200) {
        console.error("API Response:", response.data);
        throw new Error(`API error ${response.status}: ${JSON.stringify(response.data)}`);
      }

      const responseData = response.data;
      console.log("Raw API response:", JSON.stringify(responseData, null, 2));

      return responseData;
    } catch (error) {
      console.error('Error getting follow-up answer:', error);
      throw error;
    }
  },

  predictMarketOpen: async (newsItems: string, catalystData?: string): Promise<PerplexityResponse> => {
    // Check for Lambda URL instead of API key
    const lambdaUrl = import.meta.env.VITE_LAMBDA_FUNCTION_URL;
    if (!lambdaUrl) {
      throw new Error('Lambda function URL is missing. Please add VITE_LAMBDA_FUNCTION_URL to your environment variables.');
    }

    const query = `Analyze the following market news items and upcoming market catalysts to predict the S&P 500 ETF (SPY) opening price movement for the next trading day. Consider market sentiment, global developments, technical factors, and economic indicators.

CURRENT NEWS ITEMS:
${newsItems}

${catalystData ? `UPCOMING MARKET CATALYSTS:
${catalystData}

IMPORTANT: Factor in these upcoming catalysts when making your prediction. Consider how upcoming earnings reports, FOMC meetings, and economic data releases might influence market direction and volatility in the near term.` : ''}

IMPORTANT: Return a valid JSON object with the following structure. Do not include any text outside the JSON object:

{
  "prediction": "MUST BE ONE OF: Bullish, Bearish, or Neutral",
  "expected_spy_open": "MUST INCLUDE a specific percentage change, e.g. +0.5%, -0.3%, +1.2%",
  "confidence": "MUST BE ONE OF: High, Medium, or Low",
  "analysis": "Write at least 2-3 detailed paragraphs analyzing the major factors influencing your prediction. Consider both bullish and bearish signals. Explain your reasoning clearly and highlight the most impactful news items.",
  "key_factors": [
    "List at least 4-5 specific factors that support your prediction. Each factor should be a complete sentence with specific details.",
    "Include both technical and fundamental factors",
    "Mention specific news items and their impact",
    "Include market sentiment indicators",
    "Add any other relevant factors"
  ],
  "risks": [
    "List at least 3-4 specific risks that could invalidate your prediction. Each risk should be a complete sentence with specific details.",
    "Include both short-term and longer-term risks",
    "Consider counter-arguments to your main thesis",
    "Include potential market surprises or black swan events"
  ]
}

You MUST fill in all fields with detailed, specific information. Vague or generic responses are not acceptable. Your analysis should be data-driven and comprehensive. Ensure the response is a valid JSON object that can be parsed with JSON.parse().`;

    const requestData = {
      model: "sonar-pro",
      messages: [
        {
          role: "system",
          content: "You are a financial market analyst specialized in predicting S&P 500 market movements based on recent news. You provide clear, reasoned market predictions with specific directional forecasts and price targets. Your analysis is data-driven, balanced, and considers diverse factors including economic indicators, geopolitical developments, corporate news, and market sentiment. You always present both bullish and bearish perspectives before making a final prediction. IMPORTANT: You MUST fill in ALL requested fields with specific, detailed information. Do not leave any fields blank or use placeholder text. Always provide a clear prediction (Bullish, Bearish, or Neutral), a specific expected price movement with percentage, and a confidence level. Your analysis must be comprehensive and include at least 4-5 key factors and 3-4 risks to your prediction. CRITICAL: Return your response as a valid JSON object with no markdown formatting or text outside the JSON structure. The response must be parseable by JSON.parse(). DO NOT include any explanations, notes, or text outside the JSON object. ONLY return the JSON object."
        },
        {
          role: "user",
          content: query
        }
      ],
      max_tokens: 32000,
      temperature: 0.1,
      web_search_options: {
        "search_context_size": "high",
        "recency_bias": "strong"
      },
    };

    try {
      console.log("Sending market prediction request...");
      console.log("Full request to Sonarn:", JSON.stringify(requestData, null, 2));

      // Use Lambda proxy to avoid CORS issues
      const lambdaUrl = import.meta.env.VITE_LAMBDA_FUNCTION_URL;
      if (!lambdaUrl) {
        throw new Error('Lambda function URL is missing. Please add VITE_LAMBDA_FUNCTION_URL to your environment variables.');
      }
      
      const response = await axios.post(lambdaUrl, {
        action: 'perplexityRequest',
        requestData
      });

      if (response.status !== 200) {
        console.error("API Response:", response.data);
        throw new Error(`API error ${response.status}: ${JSON.stringify(response.data)}`);
      }

      const responseData = response.data;
      console.log("Raw API response:", JSON.stringify(responseData, null, 2));

      return responseData;
    } catch (error) {
      console.error('Error predicting market movements:', error);
      throw error;
    }
  },

  triggerLambdaFunction: async (): Promise<any> => {
    try {
      // Get the Lambda function URL from environment variables
      const lambdaUrl = import.meta.env.VITE_LAMBDA_FUNCTION_URL || 'https://your-lambda-function-url';

      console.log('Triggering Lambda function at:', lambdaUrl);

      const response = await axios.post(lambdaUrl, {}, {
        headers: {
          'Content-Type': 'application/json'
        },
        // Set a longer timeout since the Lambda function can take some time to complete
        timeout: 30000
      });

      console.log('Lambda function response:', response.data);
      return response.data;
    } catch (error) {
      // Check if the error is a timeout or network error
      if (axios.isAxiosError(error) && !error.response) {
        console.log('Network error or timeout, but the Lambda function might still be running');
        // Return a success message since the Lambda might still be processing
        return { message: 'Email generation triggered. It may take a few minutes to receive the email.' };
      }

      console.error('Error triggering Lambda function:', error);
      throw error;
    }
  }
};

export default apiService;