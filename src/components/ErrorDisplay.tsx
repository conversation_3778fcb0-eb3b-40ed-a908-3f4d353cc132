import React, { useState } from 'react';
import axios from 'axios';

interface ErrorDisplayProps {
  error: Error | null;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ error }) => {
  const [expanded, setExpanded] = useState(false);

  if (!error) return null;

  // Extract more specific error details for API errors
  let errorMessage = error.message;
  let statusCode: number | undefined;
  let apiErrorDetails: string | undefined;

  // Handle Axios errors
  if (axios.isAxiosError(error) && error.response) {
    statusCode = error.response.status;
    
    // Try to extract API error details
    if (error.response.data && typeof error.response.data === 'object') {
      apiErrorDetails = JSON.stringify(error.response.data, null, 2);
    }
  } 
  // Handle fetch API errors
  else {
    // Try to extract status code from the error message (e.g., "API error 400: {...}")
    const statusMatch = error.message.match(/API error (\d+):/);
    if (statusMatch && statusMatch[1]) {
      statusCode = parseInt(statusMatch[1], 10);
    }
    
    // Try to extract JSON from the error message
    try {
      const jsonMatch = error.message.match(/API error \d+:\s*(.*)/);
      if (jsonMatch && jsonMatch[1]) {
        // Try to parse as JSON
        const jsonData = JSON.parse(jsonMatch[1]);
        apiErrorDetails = JSON.stringify(jsonData, null, 2);
      }
    } catch (jsonError) {
      // Failed to parse JSON, that's fine
    }
  }

  return (
    <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md text-sm text-red-800 transition-all duration-300 hover:bg-red-100">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold text-lg flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
          API Error
        </h3>
        <button 
          className="text-red-800 hover:text-red-600 transition-colors duration-200 focus:outline-none p-1"
          onClick={() => setExpanded(!expanded)}
          aria-label={expanded ? "Collapse error details" : "Expand error details"}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 transform transition-transform duration-300 ${expanded ? 'rotate-180' : ''}`} viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      </div>
      
      <div className={`transition-all duration-500 overflow-hidden ${expanded ? 'max-h-[2000px] opacity-100' : 'max-h-20 opacity-90'}`}>
        {statusCode && (
          <div className="mb-2 flex items-center">
            <span className="font-semibold mr-2">Status Code:</span> 
            <span className={`px-2 py-1 rounded text-white ${statusCode >= 500 ? 'bg-red-600' : 'bg-yellow-600'}`}>
              {statusCode}
            </span>
          </div>
        )}
        
        <div className="mb-2">
          <span className="font-semibold">Message:</span> {errorMessage}
        </div>
        
        {apiErrorDetails && (
          <div className={`mt-3 transition-all duration-500 ${expanded ? 'opacity-100' : 'opacity-0'}`}>
            <span className="font-semibold block mb-1">API Response Details:</span>
            <pre className="bg-red-100 p-2 rounded text-xs overflow-auto max-h-40 animate-fadeIn">
              {apiErrorDetails}
            </pre>
          </div>
        )}
        
        <div className="mt-4 text-xs bg-yellow-50 p-2 rounded border border-yellow-100 animate-fadeIn">
          <p className="font-semibold">Troubleshooting tips:</p>
          <ul className="list-disc pl-5 mt-1 space-y-1">
            <li>Check that your API key is valid and properly formatted in the .env file</li>
            <li>Verify you're using the correct API endpoint</li>
            <li>Check if your account has sufficient API credits</li>
            <li>Try again with a simpler search query</li>
            {statusCode === 400 && (
              <li>Check your API request format - review the API response details for specific validation errors</li>
            )}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ErrorDisplay; 