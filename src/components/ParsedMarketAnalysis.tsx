import React from 'react';

interface ParsedMarketAnalysisProps {
  rawData: string;
}

const ParsedMarketAnalysis: React.FC<ParsedMarketAnalysisProps> = ({ rawData }) => {
  // Parse the raw data using the same logic as in MarketPrediction component
  const parsePredictionContent = (content: string) => {
    // First, try to parse as <PERSON>SO<PERSON>
    try {
      // Check if the content is a JSON string
      if (content.trim().startsWith('{') && content.trim().endsWith('}')) {
        const jsonData = JSON.parse(content);
        console.log("Parsed JSON data:", jsonData);
        return jsonData;
      }
    } catch (jsonError) {
      console.log("Not valid JSON, continuing with regex parsing");
    }

    // If not JSON, use regex parsing
    const predictionMatch = content.match(/PREDICTION:\s*([A-Za-z]+)/i);
    const expectedOpenMatch = content.match(/EXPECTED SPY OPEN:\s*([^\n]+)/i);
    const confidenceMatch = content.match(/CONFIDENCE:\s*([A-Za-z]+)/i);

    const analysisMatch = content.match(/ANALYSIS:([\s\S]+?)(?=KEY FACTORS:|$)/i);
    const keyFactorsMatch = content.match(/KEY FACTORS:([\s\S]+?)(?=RISKS TO PREDICTION:|$)/i);
    const risksMatch = content.match(/RISKS TO PREDICTION:([\s\S]+?)(?=$)/i);

    const keyFactors = keyFactorsMatch ?
      keyFactorsMatch[1].split('-').filter(f => f.trim()).map(f => f.trim()) :
      [];

    const risks = risksMatch ?
      risksMatch[1].split('-').filter(r => r.trim()).map(r => r.trim()) :
      [];

    return {
      prediction: predictionMatch ? predictionMatch[1] : 'Unknown',
      expectedOpen: expectedOpenMatch ? expectedOpenMatch[1] : 'Unknown',
      confidence: confidenceMatch ? confidenceMatch[1] : 'Medium',
      analysis: analysisMatch ? analysisMatch[1].trim() : '',
      keyFactors,
      risks,
      raw: content
    };
  };

  // Get prediction color based on prediction value
  const getPredictionColor = (prediction: string) => {
    const lowerPrediction = prediction.toLowerCase();
    if (lowerPrediction === 'bullish') return 'text-green-600';
    if (lowerPrediction === 'bearish') return 'text-red-600';
    return 'text-yellow-600'; // Neutral or unknown
  };

  // Get confidence color based on confidence level
  const getConfidenceColor = (confidence: string) => {
    const lowerConfidence = confidence.toLowerCase();
    if (lowerConfidence === 'high') return 'bg-green-600';
    if (lowerConfidence === 'low') return 'bg-red-600';
    return 'bg-yellow-600'; // Medium or unknown
  };

  // Parse the raw data
  console.log("Raw market analysis data:", rawData);

  // Try to parse as JSON first
  let prediction;
  try {
    // First, try to clean up the raw data if it contains extra text
    let cleanedData = rawData;

    // Look for JSON object pattern in the raw data
    const jsonMatch = rawData.match(/\{[\s\S]*\}/m);
    if (jsonMatch) {
      cleanedData = jsonMatch[0];
      console.log("Extracted JSON from raw data:", cleanedData);
    }

    if (cleanedData.trim().startsWith('{') && cleanedData.trim().endsWith('}')) {
      const jsonData = JSON.parse(cleanedData);
      console.log("Directly parsed JSON prediction:", jsonData);

      // Map the JSON fields to our expected format
      prediction = {
        prediction: jsonData.prediction || jsonData.market_direction || 'Neutral',
        expectedOpen: jsonData.expected_spy_open || jsonData.expectedSpyOpen || jsonData.expected_open || 'Unknown',
        confidence: jsonData.confidence || 'Medium',
        analysis: jsonData.analysis || '',
        keyFactors: Array.isArray(jsonData.key_factors) ? jsonData.key_factors :
                   (Array.isArray(jsonData.keyFactors) ? jsonData.keyFactors :
                   (typeof jsonData.key_factors === 'string' ? jsonData.key_factors.split('\n').filter(f => f.trim()).map(f => f.trim()) : [])),
        risks: Array.isArray(jsonData.risks) ? jsonData.risks :
              (Array.isArray(jsonData.risksToOutlook) ? jsonData.risksToOutlook :
              (typeof jsonData.risks === 'string' ? jsonData.risks.split('\n').filter(r => r.trim()).map(r => r.trim()) : [])),
        raw: rawData
      };
    } else {
      prediction = parsePredictionContent(rawData);
    }
  } catch (error) {
    console.error("Error parsing JSON, falling back to regex:", error);
    prediction = parsePredictionContent(rawData);
  }

  console.log("Final parsed market analysis data:", prediction);

  return (
    <div className="space-y-3">
      <div className="flex flex-wrap items-center gap-2 mb-3">
        <span className={`text-lg font-bold ${getPredictionColor(prediction.prediction)}`}>
          {prediction.prediction}
        </span>
        <span className="text-gray-500">•</span>
        <span className="text-lg font-semibold">
          {prediction.expectedOpen}
        </span>
        <span className="text-gray-500">•</span>
        <div className="flex items-center">
          <span className="text-sm text-gray-600 mr-2">Confidence:</span>
          <span className={`${getConfidenceColor(prediction.confidence)} text-white text-xs font-medium px-2 py-0.5 rounded-full`}>
            {prediction.confidence}
          </span>
        </div>
      </div>

      {/* Brief analysis summary */}
      <div className="text-sm text-gray-600 mb-2">
        {prediction.analysis.split('.')[0]}.
      </div>

      {/* Collapsible sections for additional details */}
      <details className="group">
        <summary className="flex items-center cursor-pointer list-none text-sm font-medium text-gray-700 mb-2">
          <svg className="w-4 h-4 mr-1 text-gray-500 group-open:rotate-90 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
          View Full Analysis
        </summary>
        <div className="pl-5 mt-2 space-y-3">
          <div className="mb-3">
            <h3 className="text-sm font-semibold text-gray-700 mb-1">Full Analysis</h3>
            <div className="text-gray-600 bg-gray-50 rounded-lg p-3 border border-gray-100">
              <p className="text-sm whitespace-pre-wrap">{prediction.analysis}</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {prediction.keyFactors.length > 0 && (
              <div className="bg-blue-50 rounded-lg p-3 border border-blue-100">
                <h3 className="text-sm font-semibold text-blue-800 mb-1">Key Factors</h3>
                <ul className="list-disc pl-4 space-y-1 text-sm text-blue-700">
                  {prediction.keyFactors.map((factor: string, index: number) => (
                    <li key={index}>{factor}</li>
                  ))}
                </ul>
              </div>
            )}

            {prediction.risks.length > 0 && (
              <div className="bg-red-50 rounded-lg p-3 border border-red-100">
                <h3 className="text-sm font-semibold text-red-800 mb-1">Risks to Prediction</h3>
                <ul className="list-disc pl-4 space-y-1 text-sm text-red-700">
                  {prediction.risks.map((risk: string, index: number) => (
                    <li key={index}>{risk}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      </details>
    </div>
  );
};

export default ParsedMarketAnalysis;
