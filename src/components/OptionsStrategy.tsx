import React, { useMemo, useEffect, useState } from 'react';
import OptionsStrategyChart from './OptionsStrategyChart';
import OptionsStrategyBuilder from './OptionsStrategyBuilder';
import RelatedStocksTable, { RelatedStock } from './RelatedStocksTable';

// Extend Window interface to add our global variable for TypeScript
declare global {
  interface Window {
    optionsStrategyData?: string;
  }
}

interface OptionsStrategyProps {
  strategyData: string;
  isLoading: boolean;
  error: Error | null;
}

interface ParsedStrategy {
  strategyName: string;
  overview: string;
  entryTime: string;
  expirationTimeframe: string;
  strikeSelection: string;
  impliedVolatility: string;
  positionSize: string;
  riskReward: string;
  profitPotential: string;
  maxLoss: string;
  breakEven: string;
  marketEnvironment: {
    trend: string;
    volatility: string;
    sentiment: string;
  };
  indicators: { name: string; condition: string; interpretation: string }[];
  riskManagement: { type: string; value: string }[];
  relatedStocks: RelatedStock[];
}

const OptionsStrategy: React.FC<OptionsStrategyProps> = ({ strategyData, isLoading, error }) => {
  // Always execute all hooks at the top level

  // State for showing raw JSON
  const [showRawJson, setShowRawJson] = useState(false);

  // Store the raw strategy data in a global variable for context sharing
  useEffect(() => {
    // Make the data available to other components via global variable
    window.optionsStrategyData = strategyData;

    // Clean up when component unmounts
    return () => {
      window.optionsStrategyData = undefined;
    };
  }, [strategyData]);

  // Parse strategy data from JSON response
  const parsedStrategy = useMemo(() => {
    if (!strategyData) return null;

    // Only log in development mode and not on every render
    if (process.env.NODE_ENV === 'development') {
      console.log("[OptionsStrategy] Raw strategy data (once):", strategyData.substring(0, 100) + '...');
    }

    try {
      // First try to parse as JSON
      // Look for a JSON object pattern in the content
      const jsonMatch = strategyData.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          // Try to parse the JSON object
          const jsonData = JSON.parse(jsonMatch[0]);
          // Only log in development mode
          if (process.env.NODE_ENV === 'development') {
            console.log("[OptionsStrategy] Parsed JSON data (once):", jsonData);
          }

          // Direct field extraction - use lowercase keys for case-insensitive matching
          const normalizedData = {};

          // Convert all keys to lowercase for case-insensitive matching
          Object.keys(jsonData).forEach(key => {
            normalizedData[key.toLowerCase()] = jsonData[key];
          });

          // Extract strike prices - handle both string and object formats
          let strikeSelection = '';
          if (normalizedData['strike_selection']) {
            strikeSelection = normalizedData['strike_selection'];
          } else if (normalizedData['strike_prices']) {
            if (typeof normalizedData['strike_prices'] === 'object') {
              // Handle object format
              strikeSelection = Object.entries(normalizedData['strike_prices'])
                .map(([type, price]) => `${type.replace('_', ' ')}: $${price}`)
                .join(', ');
            } else {
              // Handle string format
              strikeSelection = normalizedData['strike_prices'];
            }
          }

          // Handle market environment data
          const marketEnvironment = {
            trend: normalizedData['current_trend'] ||
                  (normalizedData['market_environment'] && normalizedData['market_environment']['current_trend']) ||
                  "Not specified",
            volatility: normalizedData['volatility_environment'] ||
                       (normalizedData['market_environment'] && normalizedData['market_environment']['volatility_environment']) ||
                       "Not specified",
            sentiment: normalizedData['sentiment'] ||
                      (normalizedData['market_environment'] && normalizedData['market_environment']['sentiment']) ||
                      "Not specified"
          };

          // Create properly structured indicators
          const indicators = [];

          // Check for new format indicators first
          if (normalizedData['indicators'] && Array.isArray(normalizedData['indicators'])) {
            // New format with array of indicators
            normalizedData['indicators'].forEach(indicator => {
              indicators.push({
                name: indicator.name,
                condition: indicator.condition,
                interpretation: indicator.interpretation
              });
            });
          } else {
            // Try to extract indicators from the raw JSON structure
            // Look for patterns like "indicator_1_name" or similar
            const indicatorKeys = Object.keys(normalizedData).filter(key =>
              key.includes('indicator') && (key.includes('name') || key.includes('condition') || key.includes('interpretation'))
            );

            // Group indicator keys by their number
            const indicatorGroups = {};
            indicatorKeys.forEach(key => {
              // Extract the indicator number
              const match = key.match(/indicator[_]?(\d+)[_]?/);
              if (match && match[1]) {
                const num = match[1];
                if (!indicatorGroups[num]) {
                  indicatorGroups[num] = [];
                }
                indicatorGroups[num].push(key);
              }
            });

            // Process each indicator group
            Object.keys(indicatorGroups).forEach(num => {
              const group = indicatorGroups[num];
              let name = "Indicator " + num;
              let condition = "Not specified";
              let interpretation = "Not specified";

              // Find the name, condition, and interpretation keys
              group.forEach(key => {
                if (key.includes('name')) {
                  name = normalizedData[key] || name;
                } else if (key.includes('condition') || key.includes('value')) {
                  condition = normalizedData[key] || condition;
                } else if (key.includes('interpretation')) {
                  interpretation = normalizedData[key] || interpretation;
                }
              });

              indicators.push({ name, condition, interpretation });
            });
          }

          // Create risk management items
          const riskManagement = [];

          // Check for new format first
          if (normalizedData['riskmanagement'] || normalizedData['risk_management']) {
            const riskMgmt = normalizedData['riskmanagement'] || normalizedData['risk_management'];

            if (riskMgmt.stoploss || riskMgmt.stop_loss) {
              riskManagement.push({
                type: 'Stop Loss',
                value: riskMgmt.stoploss || riskMgmt.stop_loss
              });
            }

            if (riskMgmt.takeprofit || riskMgmt.take_profit) {
              riskManagement.push({
                type: 'Take Profit',
                value: riskMgmt.takeprofit || riskMgmt.take_profit
              });
            }

            if (riskMgmt.adjustmentcriteria || riskMgmt.adjustment_criteria) {
              riskManagement.push({
                type: 'Adjustment Criteria',
                value: riskMgmt.adjustmentcriteria || riskMgmt.adjustment_criteria
              });
            }
          } else {
            // Direct field extraction
            if (normalizedData['stop_loss']) {
              riskManagement.push({
                type: 'Stop Loss',
                value: normalizedData['stop_loss']
              });
            }

            if (normalizedData['take_profit']) {
              riskManagement.push({
                type: 'Take Profit',
                value: normalizedData['take_profit']
              });
            }

            if (normalizedData['adjustment_criteria']) {
              riskManagement.push({
                type: 'Adjustment Criteria',
                value: normalizedData['adjustment_criteria']
              });
            }
          }

          // Create related stocks
          const relatedStocks = [];

          // Check if we have the new format (array of objects)
          if (normalizedData['relatedstocks'] || normalizedData['related_stocks']) {
            const stocks = normalizedData['relatedstocks'] || normalizedData['related_stocks'];

            if (Array.isArray(stocks)) {
              stocks.forEach(stock => {
                if (stock.ticker || stock.symbol) {
                  relatedStocks.push({
                    symbol: stock.ticker || stock.symbol,
                    recommendation: stock.recommendation as 'Buy' | 'Sell' | 'Hold',
                    rationale: stock.reason || stock.rationale || '',
                    targetPrice: stock.target || stock.targetPrice,
                    outlook: stock.outlook
                  });
                }
              });
            }
          } else {
            // Try to find related stocks in the flat structure
            const stockKeys = Object.keys(normalizedData).filter(key =>
              key.includes('related_stock') || key.includes('relatedstock')
            );

            // Group stock keys by their number
            const stockGroups = {};
            stockKeys.forEach(key => {
              // Extract the stock number
              const match = key.match(/(related_stock|relatedstock)[_]?(\d+)[_]?/);
              if (match && match[2]) {
                const num = match[2];
                if (!stockGroups[num]) {
                  stockGroups[num] = [];
                }
                stockGroups[num].push(key);
              }
            });

            // Process each stock group
            Object.keys(stockGroups).forEach(num => {
              const group = stockGroups[num];
              let symbol = '';
              let recommendation = 'Hold' as 'Buy' | 'Sell' | 'Hold';
              let rationale = '';
              let targetPrice;
              let outlook;

              // Find the symbol, recommendation, rationale, target, and outlook keys
              group.forEach(key => {
                if (key.includes('symbol') || key.includes('ticker')) {
                  symbol = normalizedData[key] || symbol;
                } else if (key.includes('recommendation')) {
                  recommendation = normalizedData[key] as 'Buy' | 'Sell' | 'Hold' || recommendation;
                } else if (key.includes('rationale') || key.includes('reason')) {
                  rationale = normalizedData[key] || rationale;
                } else if (key.includes('target')) {
                  targetPrice = normalizedData[key] || targetPrice;
                } else if (key.includes('outlook')) {
                  outlook = normalizedData[key] || outlook;
                }
              });

              if (symbol) {
                relatedStocks.push({ symbol, recommendation, rationale, targetPrice, outlook });
              }
            });
          }

          const strategy: ParsedStrategy = {
            strategyName: normalizedData['strategy_name'] || normalizedData['recommendation'] || '',
            overview: normalizedData['overview'] || normalizedData['analysis'] || '',
            entryTime: normalizedData['entry_time'] || (normalizedData['strategy'] && normalizedData['strategy']['timeframe']) || '',
            expirationTimeframe: normalizedData['expiration_timeframe'] || normalizedData['expiration'] ||
                               (normalizedData['strategy'] && normalizedData['strategy']['timeframe']) || '',
            strikeSelection: strikeSelection || (normalizedData['strategy'] && normalizedData['strategy']['strikeselection']) || '',
            impliedVolatility: normalizedData['implied_volatility'] ||
                             (normalizedData['strategy'] && normalizedData['strategy']['impliedvolatility']) || '',
            positionSize: normalizedData['position_size'] ||
                        (normalizedData['strategy'] && normalizedData['strategy']['positionsize']) || '',
            riskReward: normalizedData['risk_reward'] ||
                      (normalizedData['strategy'] && normalizedData['strategy']['riskdescription']) || '',
            profitPotential: normalizedData['profit_potential'] ||
                          (normalizedData['strategy'] && normalizedData['strategy']['potentialreturn']) ||
                          normalizedData['max_profit'] || '',
            maxLoss: normalizedData['max_loss'] ||
                   (normalizedData['strategy'] && normalizedData['strategy']['riskdescription']) || '',
            breakEven: normalizedData['break_even'] ||
                     (normalizedData['strategy'] && normalizedData['strategy']['breakevendescription']) || '',
            marketEnvironment,
            indicators,
            riskManagement,
            relatedStocks
          };

          console.log("Structured strategy:", strategy);
          return strategy;
        } catch (jsonError) {
          console.error("Error parsing JSON:", jsonError);
          // If JSON parsing fails, fall back to regex approach
        }
      }

      // Fallback to regex parsing for plain text format
      const strategy: Partial<ParsedStrategy> = {
        strategyName: '',
        overview: '',
        entryTime: '',
        expirationTimeframe: '',
        strikeSelection: '',
        impliedVolatility: '',
        positionSize: '',
        riskReward: '',
        profitPotential: '',
        maxLoss: '',
        breakEven: '',
        marketEnvironment: {
          trend: 'Not specified',
          volatility: 'Not specified',
          sentiment: 'Not specified'
        },
        indicators: [],
        riskManagement: [],
        relatedStocks: []
      };

      // Try to extract data from JSON-like format in the text
      // This handles the format shown in the screenshot
      const jsonFieldRegex = /"([^"]+)":\s*"([^"]+)"/g;
      let match;
      while ((match = jsonFieldRegex.exec(strategyData)) !== null) {
        const [_, key, value] = match;
        const normalizedKey = key.toLowerCase();

        // Map the extracted fields to our strategy object
        if (normalizedKey === 'strategy_name') {
          strategy.strategyName = value.trim();
        } else if (normalizedKey === 'overview') {
          strategy.overview = value.trim();
        } else if (normalizedKey === 'entry_time') {
          strategy.entryTime = value.trim();
        } else if (normalizedKey === 'expiration_timeframe') {
          strategy.expirationTimeframe = value.trim();
        } else if (normalizedKey === 'strike_selection') {
          strategy.strikeSelection = value.trim();
        } else if (normalizedKey === 'implied_volatility') {
          strategy.impliedVolatility = value.trim();
        } else if (normalizedKey === 'position_size') {
          strategy.positionSize = value.trim();
        } else if (normalizedKey === 'risk_reward') {
          strategy.riskReward = value.trim();
        } else if (normalizedKey === 'profit_potential') {
          strategy.profitPotential = value.trim();
        } else if (normalizedKey === 'max_loss') {
          strategy.maxLoss = value.trim();
        } else if (normalizedKey === 'break_even') {
          strategy.breakEven = value.trim();
        } else if (normalizedKey === 'current_trend') {
          strategy.marketEnvironment.trend = value.trim();
        } else if (normalizedKey === 'volatility_environment') {
          strategy.marketEnvironment.volatility = value.trim();
        } else if (normalizedKey === 'sentiment') {
          strategy.marketEnvironment.sentiment = value.trim();
        }
      }

      // If we still don't have a strategy name, try to extract it from the first line
      if (!strategy.strategyName) {
        const firstLineMatch = strategyData.match(/^\s*"([^"]+)"/);
        if (firstLineMatch) {
          strategy.strategyName = firstLineMatch[1].trim();
        }
      }

      // Extract indicators
      const indicatorRegex = /"indicator_(\d+)_name":\s*"([^"]+)"/g;
      let indicatorMatch;
      while ((indicatorMatch = indicatorRegex.exec(strategyData)) !== null) {
        const [_, num, name] = indicatorMatch;

        // Look for corresponding condition and interpretation
        const conditionRegex = new RegExp(`"indicator_${num}_condition":\s*"([^"]+)"`);
        const interpretationRegex = new RegExp(`"indicator_${num}_interpretation":\s*"([^"]+)"`);

        const conditionMatch = strategyData.match(conditionRegex);
        const interpretationMatch = strategyData.match(interpretationRegex);

        strategy.indicators.push({
          name: name.trim(),
          condition: conditionMatch ? conditionMatch[1].trim() : 'Not specified',
          interpretation: interpretationMatch ? interpretationMatch[1].trim() : 'Not specified'
        });
      }

      // Extract risk management
      if (strategyData.includes('"stop_loss"')) {
        const stopLossMatch = strategyData.match(/"stop_loss":\s*"([^"]+)"/);
        if (stopLossMatch) {
          strategy.riskManagement.push({
            type: 'Stop Loss',
            value: stopLossMatch[1].trim()
          });
        }
      }

      if (strategyData.includes('"take_profit"')) {
        const takeProfitMatch = strategyData.match(/"take_profit":\s*"([^"]+)"/);
        if (takeProfitMatch) {
          strategy.riskManagement.push({
            type: 'Take Profit',
            value: takeProfitMatch[1].trim()
          });
        }
      }

      // Fallback to traditional format if we still don't have data
      if (!strategy.strategyName) {
        // Extract strategy name
        const strategyNameMatch = strategyData.match(/STRATEGY_NAME:\s*([^\n]+)/);
        if (strategyNameMatch) strategy.strategyName = strategyNameMatch[1].trim();

        // Extract overview
        const overviewMatch = strategyData.match(/OVERVIEW:\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\n\w+:)/);
        if (overviewMatch) strategy.overview = overviewMatch[1].trim();

        // Extract trade details
        const entryTimeMatch = strategyData.match(/ENTRY_TIME:\s*([^\n]+)/);
        if (entryTimeMatch) strategy.entryTime = entryTimeMatch[1].trim();

        // Try new format first, then fall back to old format
        const expirationTimeframeMatch = strategyData.match(/EXPIRATION_TIMEFRAME:\s*([^\n]+)/);
        const expirationMatch = strategyData.match(/EXPIRATION:\s*([^\n]+)/);
        if (expirationTimeframeMatch) {
          strategy.expirationTimeframe = expirationTimeframeMatch[1].trim();
        } else if (expirationMatch) {
          strategy.expirationTimeframe = expirationMatch[1].trim();
        }
      }

      // Try new format first, then fall back to old format
      const strikeSelectionMatch = strategyData.match(/STRIKE_SELECTION:\s*([^\n]+)/);
      const strikePriceMatch = strategyData.match(/STRIKE_PRICES:\s*([^\n]+)/);
      if (strikeSelectionMatch) {
        strategy.strikeSelection = strikeSelectionMatch[1].trim();
      } else if (strikePriceMatch) {
        strategy.strikeSelection = strikePriceMatch[1].trim();
      }

      // Extract new fields
      const impliedVolatilityMatch = strategyData.match(/IMPLIED_VOLATILITY:\s*([^\n]+)/);
      if (impliedVolatilityMatch) strategy.impliedVolatility = impliedVolatilityMatch[1].trim();

      const positionSizeMatch = strategyData.match(/POSITION_SIZE:\s*([^\n]+)/);
      if (positionSizeMatch) strategy.positionSize = positionSizeMatch[1].trim();

      const riskRewardMatch = strategyData.match(/RISK_REWARD:\s*([^\n]+)/);
      if (riskRewardMatch) strategy.riskReward = riskRewardMatch[1].trim();

      // Try new format first, then fall back to old format
      const profitPotentialMatch = strategyData.match(/PROFIT_POTENTIAL:\s*([^\n]+)/);
      const maxProfitMatch = strategyData.match(/MAX_PROFIT:\s*([^\n]+)/);
      if (profitPotentialMatch) {
        strategy.profitPotential = profitPotentialMatch[1].trim();
      } else if (maxProfitMatch) {
        strategy.profitPotential = maxProfitMatch[1].trim();
      }

      const maxLossMatch = strategyData.match(/MAX_LOSS:\s*([^\n]+)/);
      if (maxLossMatch) strategy.maxLoss = maxLossMatch[1].trim();

      const breakEvenMatch = strategyData.match(/BREAK_EVEN:\s*([^\n]+)/);
      if (breakEvenMatch) strategy.breakEven = breakEvenMatch[1].trim();

      // Extract market environment
      const currentTrendMatch = strategyData.match(/CURRENT_TREND:\s*([^\n]+)/);
      if (currentTrendMatch) strategy.marketEnvironment.trend = currentTrendMatch[1].trim();

      const volatilityEnvMatch = strategyData.match(/VOLATILITY_ENVIRONMENT:\s*([^\n]+)/);
      if (volatilityEnvMatch) strategy.marketEnvironment.volatility = volatilityEnvMatch[1].trim();

      const sentimentMatch = strategyData.match(/SENTIMENT:\s*([^\n]+)/);
      if (sentimentMatch) strategy.marketEnvironment.sentiment = sentimentMatch[1].trim();

      // Extract indicators
      const indicators = [];

      // Try to match new format indicators
      // Indicator 1
      const indicator1NameMatch = strategyData.match(/INDICATOR_1_NAME:\s*([^\n]+)/);
      const indicator1ConditionMatch = strategyData.match(/INDICATOR_1_CONDITION:\s*([^\n]+)/) ||
                                      strategyData.match(/INDICATOR_1_VALUE:\s*([^\n]+)/);
      const indicator1InterpMatch = strategyData.match(/INDICATOR_1_INTERPRETATION:\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\n\w+:|$)/);

      if (indicator1NameMatch && (indicator1ConditionMatch || indicator1InterpMatch)) {
        indicators.push({
          name: indicator1NameMatch[1].trim(),
          condition: indicator1ConditionMatch ? indicator1ConditionMatch[1].trim() : 'Not specified',
          interpretation: indicator1InterpMatch ? indicator1InterpMatch[1].trim() : 'Not specified'
        });
      }

      // Indicator 2
      const indicator2NameMatch = strategyData.match(/INDICATOR_2_NAME:\s*([^\n]+)/);
      const indicator2ConditionMatch = strategyData.match(/INDICATOR_2_CONDITION:\s*([^\n]+)/) ||
                                      strategyData.match(/INDICATOR_2_VALUE:\s*([^\n]+)/);
      const indicator2InterpMatch = strategyData.match(/INDICATOR_2_INTERPRETATION:\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\n\w+:|$)/);

      if (indicator2NameMatch && (indicator2ConditionMatch || indicator2InterpMatch)) {
        indicators.push({
          name: indicator2NameMatch[1].trim(),
          condition: indicator2ConditionMatch ? indicator2ConditionMatch[1].trim() : 'Not specified',
          interpretation: indicator2InterpMatch ? indicator2InterpMatch[1].trim() : 'Not specified'
        });
      }

      // Indicator 3
      const indicator3NameMatch = strategyData.match(/INDICATOR_3_NAME:\s*([^\n]+)/);
      const indicator3ConditionMatch = strategyData.match(/INDICATOR_3_CONDITION:\s*([^\n]+)/) ||
                                      strategyData.match(/INDICATOR_3_VALUE:\s*([^\n]+)/);
      const indicator3InterpMatch = strategyData.match(/INDICATOR_3_INTERPRETATION:\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\n\w+:|$)/);

      if (indicator3NameMatch && (indicator3ConditionMatch || indicator3InterpMatch)) {
        indicators.push({
          name: indicator3NameMatch[1].trim(),
          condition: indicator3ConditionMatch ? indicator3ConditionMatch[1].trim() : 'Not specified',
          interpretation: indicator3InterpMatch ? indicator3InterpMatch[1].trim() : 'Not specified'
        });
      }

      strategy.indicators = indicators;

      // Extract risk management - look for both formats
      const riskManagement = [];

      // Try new format (under RISK_MANAGEMENT section)
      const stopLossRegex = /RISK_MANAGEMENT[^]*?STOP_LOSS:\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\n\w+:|$|\n\nTAKE_PROFIT)/;
      const takeProfitRegex = /RISK_MANAGEMENT[^]*?TAKE_PROFIT:\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\n\w+:|$|\n\nADJUSTMENT_CRITERIA)/;
      const adjustmentRegex = /RISK_MANAGEMENT[^]*?ADJUSTMENT_CRITERIA:\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\n\w+:|$)/;

      // Check both new and old formats
      const stopLossMatch = strategyData.match(stopLossRegex) || strategyData.match(/STOP_LOSS:\s*([^\n]+)/);
      if (stopLossMatch) {
        riskManagement.push({
          type: 'Stop Loss',
          value: stopLossMatch[1].trim()
        });
      }

      const takeProfitMatch = strategyData.match(takeProfitRegex) || strategyData.match(/TAKE_PROFIT:\s*([^\n]+)/);
      if (takeProfitMatch) {
        riskManagement.push({
          type: 'Take Profit',
          value: takeProfitMatch[1].trim()
        });
      }

      const adjustmentMatch = strategyData.match(adjustmentRegex) || strategyData.match(/ADJUSTMENT_CRITERIA:\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\n\w+:|$)/);
      if (adjustmentMatch) {
        riskManagement.push({
          type: 'Adjustment Criteria',
          value: adjustmentMatch[1].trim()
        });
      }

      strategy.riskManagement = riskManagement;

      // Extract stocks using regex
      const relatedStocks = [];

      for (let i = 1; i <= 5; i++) {
        const symbolMatch = strategyData.match(new RegExp(`RELATED_STOCK_${i}_SYMBOL:\\s*([^\\n]+)`));
        const recMatch = strategyData.match(new RegExp(`RELATED_STOCK_${i}_RECOMMENDATION:\\s*([^\\n]+)`));
        const rationaleMatch = strategyData.match(new RegExp(`RELATED_STOCK_${i}_RATIONALE:\\s*([^\\n]+(?:\\n[^\\n]+)*?)(?=\\n\\n\\w+:|$)`));
        const targetMatch = strategyData.match(new RegExp(`RELATED_STOCK_${i}_TARGET:\\s*([^\\n]+)`));
        const outlookMatch = strategyData.match(new RegExp(`RELATED_STOCK_${i}_OUTLOOK:\\s*([^\\n]+(?:\\n[^\\n]+)*?)(?=\\n\\n\\w+:|$)`));

        if (symbolMatch) {
          relatedStocks.push({
            symbol: symbolMatch[1].trim(),
            recommendation: (recMatch?.[1].trim() as 'Buy' | 'Sell' | 'Hold') || 'Hold',
            rationale: rationaleMatch?.[1].trim() || '',
            targetPrice: targetMatch?.[1].trim() || undefined,
            outlook: outlookMatch?.[1].trim() || undefined
          });
        }
      }

      strategy.relatedStocks = relatedStocks.filter(stock => stock.symbol);

      // Only log in development mode and not on every render
      if (process.env.NODE_ENV === 'development' && strategy) {
        // Using a more specific console log name to identify the source
        console.log("[OptionsStrategy] Parsed strategy once:", strategy);
      }
      return strategy as ParsedStrategy;
    } catch (e) {
      console.error("Error parsing strategy data:", e);
      return null;
    }
  }, [strategyData]);

  // Generate accurate payoff data for the chart based on strategy type
  const payoffData = useMemo(() => {
    if (!parsedStrategy) return [];

    // Determine strategy type
    const strategyName = parsedStrategy.strategyName.toLowerCase();
    const isBullish = strategyName.includes('bull') ||
                      (strategyName.includes('call') && !strategyName.includes('bear'));
    const isBearish = strategyName.includes('bear') ||
                      (strategyName.includes('put') && !strategyName.includes('bull'));

    // Extract strike prices from text - handle multiple strikes for spreads
    let sellStrike = 0;
    let buyStrike = 0;

    // First try to handle structured strike prices (for spreads)
    const strikeSelection = parsedStrategy.strikeSelection || '';
    const sellCallMatch = strikeSelection.match(/sell\s+call\s*:\s*\$?(\d+(?:\.\d+)?)/i);
    const buyCallMatch = strikeSelection.match(/buy\s+call\s*:\s*\$?(\d+(?:\.\d+)?)/i);
    const sellPutMatch = strikeSelection.match(/sell\s+put\s*:\s*\$?(\d+(?:\.\d+)?)/i);
    const buyPutMatch = strikeSelection.match(/buy\s+put\s*:\s*\$?(\d+(?:\.\d+)?)/i);

    // Extract profit/loss amounts with proper handling of dollar signs
    let maxProfit = 0;
    const profitStr = parsedStrategy.profitPotential || parsedStrategy.maxProfit || '';
    const maxProfitMatch = profitStr.match(/\$?(\d+(?:\.\d+)?)/);
    if (maxProfitMatch) {
      maxProfit = parseFloat(maxProfitMatch[1]);
    }

    let maxLoss = 0;
    const lossStr = parsedStrategy.maxLoss || '';
    const maxLossMatch = lossStr.match(/\$?(\d+(?:\.\d+)?)/);
    if (maxLossMatch) {
      maxLoss = parseFloat(maxLossMatch[1]);
    }

    // Extract break-even points
    const breakEvenPoints: number[] = [];
    const breakEvenStr = parsedStrategy.breakEven || '';
    const breakEvenMatch = breakEvenStr.match(/\$?(\d+(?:\.\d+)?)/g);
    if (breakEvenMatch) {
      breakEvenMatch.forEach(match => {
        const point = parseFloat(match.replace(/\$/g, '').trim());
        if (!isNaN(point)) {
          breakEvenPoints.push(point);
        }
      });
    }

    // For the chart, we need to determine the range of price to show
    let midPoint = 0;
    let range = 0;

    // Determine the midpoint and range for the price axis
    if (isBullish && sellCallMatch && buyCallMatch) {
      // Bull call spread
      sellStrike = parseFloat(sellCallMatch[1]);
      buyStrike = parseFloat(buyCallMatch[1]);
      midPoint = (sellStrike + buyStrike) / 2;
      range = (buyStrike - sellStrike) * 3; // 3x width to show context
    } else if (isBearish && sellPutMatch && buyPutMatch) {
      // Bear put spread
      sellStrike = parseFloat(sellPutMatch[1]);
      buyStrike = parseFloat(buyPutMatch[1]);
      midPoint = (sellStrike + buyStrike) / 2;
      range = (buyStrike - sellStrike) * 3; // 3x width to show context
    } else if (isBearish && sellCallMatch && buyCallMatch) {
      // Bear call spread
      sellStrike = parseFloat(sellCallMatch[1]);
      buyStrike = parseFloat(buyCallMatch[1]);
      midPoint = (sellStrike + buyStrike) / 2;
      range = (buyStrike - sellStrike) * 3; // 3x width to show context
    } else if (breakEvenPoints.length > 0) {
      // Use break-even points if available
      if (breakEvenPoints.length >= 2) {
        midPoint = (breakEvenPoints[0] + breakEvenPoints[1]) / 2;
        range = (breakEvenPoints[1] - breakEvenPoints[0]) * 3;
      } else {
        midPoint = breakEvenPoints[0];
        range = midPoint * 0.1; // 10% of midpoint for single break-even
      }
    } else {
      // Default fallback if we can't determine specific strikes
      // Try to extract a single strike price
      const singleStrikeMatch = strikeSelection.match(/\$?(\d+(?:\.\d+)?)/);
      if (singleStrikeMatch) {
        midPoint = parseFloat(singleStrikeMatch[1]);
      } else {
        midPoint = 450; // Default value if we can't extract anything
      }
      range = midPoint * 0.1; // 10% of midpoint
    }

    // Calculate min and max prices for the chart
    const minPrice = Math.max(midPoint - range, 0); // Avoid negative prices
    const maxPrice = midPoint + range;

    // Generate points for the payoff chart
    const points: Array<{price: number, profit: number}> = [];
    const steps = 40; // Number of points to generate
    const step = (maxPrice - minPrice) / steps;

    // Generate payoff curve based on strategy type and available information
    if (isBullish && sellStrike && buyStrike) {
      // Bull call spread
      for (let price = minPrice; price <= maxPrice; price += step) {
        let profit;
        if (price <= sellStrike) {
          // Below lower strike - max loss
          profit = -maxLoss;
        } else if (price >= buyStrike) {
          // Above upper strike - max profit
          profit = maxProfit;
        } else {
          // Between strikes - linear increase
          const ratio = (price - sellStrike) / (buyStrike - sellStrike);
          profit = -maxLoss + ratio * (maxProfit + maxLoss);
        }
        points.push({ price, profit });
      }
    } else if (isBearish && sellStrike && buyStrike) {
      // Bear put spread OR bear call spread
      let lowerStrike, upperStrike;
      if (sellStrike < buyStrike) {
        // Bear call spread
        lowerStrike = sellStrike;
        upperStrike = buyStrike;

        for (let price = minPrice; price <= maxPrice; price += step) {
          let profit;
          if (price <= lowerStrike) {
            // Below lower strike - max profit
            profit = maxProfit;
          } else if (price >= upperStrike) {
            // Above upper strike - max loss
            profit = -maxLoss;
          } else {
            // Between strikes - linear decrease
            const ratio = (price - lowerStrike) / (upperStrike - lowerStrike);
            profit = maxProfit - ratio * (maxProfit + maxLoss);
          }
          points.push({ price, profit });
        }
      } else {
        // Bear put spread
        lowerStrike = buyStrike;
        upperStrike = sellStrike;

        for (let price = minPrice; price <= maxPrice; price += step) {
          let profit;
          if (price <= lowerStrike) {
            // Below lower strike - max profit
            profit = maxProfit;
          } else if (price >= upperStrike) {
            // Above upper strike - max loss
            profit = -maxLoss;
          } else {
            // Between strikes - linear decrease
            const ratio = (price - lowerStrike) / (upperStrike - lowerStrike);
            profit = maxProfit - ratio * (maxProfit + maxLoss);
          }
          points.push({ price, profit });
        }
      }
    } else if (breakEvenPoints.length >= 2) {
      // Use break-even points for neutral strategies like iron condors
      const lowerBreakEven = Math.min(...breakEvenPoints);
      const upperBreakEven = Math.max(...breakEvenPoints);
      const width = (upperBreakEven - lowerBreakEven) / 2;

      for (let price = minPrice; price <= maxPrice; price += step) {
        let profit;
        if (price >= lowerBreakEven && price <= upperBreakEven) {
          // Between break-evens - max profit
          profit = maxProfit;
        } else if (price < lowerBreakEven) {
          // Below lower break-even
          const lowerBound = lowerBreakEven - width;
          if (price <= lowerBound) {
            profit = -maxLoss;
          } else {
            const ratio = (price - lowerBound) / (lowerBreakEven - lowerBound);
            profit = -maxLoss + ratio * (maxProfit + maxLoss);
          }
        } else {
          // Above upper break-even
          const upperBound = upperBreakEven + width;
          if (price >= upperBound) {
            profit = -maxLoss;
          } else {
            const ratio = (price - upperBreakEven) / (upperBound - upperBreakEven);
            profit = maxProfit - ratio * (maxProfit + maxLoss);
          }
        }
        points.push({ price, profit });
      }
    } else if (breakEvenPoints.length === 1) {
      // Single break-even point strategies
      const breakEven = breakEvenPoints[0];

      if (isBullish) {
        // For bullish strategies with a single break-even
        for (let price = minPrice; price <= maxPrice; price += step) {
          let profit;
          if (price <= breakEven) {
            // Below break-even - loss
            profit = ((price / breakEven) - 1) * maxLoss;
          } else {
            // Above break-even - profit increases with price
            const maxPriceRatio = 1.5; // How much above break-even to reach max profit
            const maxProfitPoint = breakEven * maxPriceRatio;

            if (price >= maxProfitPoint) {
              profit = maxProfit;
            } else {
              const ratio = (price - breakEven) / (maxProfitPoint - breakEven);
              profit = ratio * maxProfit;
            }
          }
          points.push({ price, profit });
        }
      } else if (isBearish) {
        // For bearish strategies with a single break-even
        for (let price = minPrice; price <= maxPrice; price += step) {
          let profit;
          if (price >= breakEven) {
            // Above break-even - loss
            profit = (1 - (breakEven / price)) * -maxLoss;
          } else {
            // Below break-even - profit increases as price decreases
            const minPriceRatio = 0.5; // How much below break-even to reach max profit
            const maxProfitPoint = breakEven * minPriceRatio;

            if (price <= maxProfitPoint) {
              profit = maxProfit;
            } else {
              const ratio = (breakEven - price) / (breakEven - maxProfitPoint);
              profit = ratio * maxProfit;
            }
          }
          points.push({ price, profit });
        }
      } else {
        // Neutral strategy with single break-even (unusual, fallback implementation)
        for (let price = minPrice; price <= maxPrice; price += step) {
          const distance = Math.abs(price - breakEven);
          const maxDistance = breakEven * 0.2; // 20% distance to reach max loss

          let profit;
          if (distance === 0) {
            profit = maxProfit;
          } else if (distance >= maxDistance) {
            profit = -maxLoss;
          } else {
            profit = maxProfit - (distance / maxDistance) * (maxProfit + maxLoss);
          }
          points.push({ price, profit });
        }
      }
    } else {
      // Fallback for when we don't have enough information
      // Create a generic curve based on strategy type
      if (isBullish) {
        // Generic bullish payoff (long call-like)
        const midPoint = (minPrice + maxPrice) / 2;

        for (let price = minPrice; price <= maxPrice; price += step) {
          let profit;
          if (price <= midPoint) {
            profit = -maxLoss;
          } else {
            const ratio = (price - midPoint) / (maxPrice - midPoint);
            profit = -maxLoss + ratio * (maxProfit + maxLoss);
            profit = Math.min(profit, maxProfit);
          }
          points.push({ price, profit });
        }
      } else if (isBearish) {
        // Generic bearish payoff (long put-like)
        const midPoint = (minPrice + maxPrice) / 2;

        for (let price = minPrice; price <= maxPrice; price += step) {
          let profit;
          if (price >= midPoint) {
            profit = -maxLoss;
          } else {
            const ratio = (midPoint - price) / (midPoint - minPrice);
            profit = -maxLoss + ratio * (maxProfit + maxLoss);
            profit = Math.min(profit, maxProfit);
          }
          points.push({ price, profit });
        }
      } else {
        // Generic neutral payoff (iron condor-like)
        const range = maxPrice - minPrice;
        const lowerThird = minPrice + range / 3;
        const upperThird = maxPrice - range / 3;

        for (let price = minPrice; price <= maxPrice; price += step) {
          let profit;
          if (price >= lowerThird && price <= upperThird) {
            profit = maxProfit;
          } else if (price < lowerThird) {
            const ratio = (price - minPrice) / (lowerThird - minPrice);
            profit = -maxLoss + ratio * (maxProfit + maxLoss);
          } else {
            const ratio = (price - upperThird) / (maxPrice - upperThird);
            profit = maxProfit - ratio * (maxProfit + maxLoss);
          }
          points.push({ price, profit });
        }
      }
    }

    return points;
  }, [parsedStrategy]);

  // Handle loading state
  if (isLoading) {
    return (
      <div className="bg-white rounded-lg p-6 shadow-md mb-6 animate-pulse">
        <div className="h-7 bg-gray-200 rounded-md w-1/3 mb-4"></div>
        <div className="space-y-3">
          <div className="h-4 bg-gray-200 rounded-md w-full"></div>
          <div className="h-4 bg-gray-200 rounded-md w-5/6"></div>
          <div className="h-4 bg-gray-200 rounded-md w-4/6"></div>
        </div>
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
        <h3 className="text-lg font-semibold text-red-700 mb-2">Error Generating Options Strategy</h3>
        <p className="text-red-600">{error.message}</p>
      </div>
    );
  }

  // If no strategy data after parsing, return null
  if (!parsedStrategy) {
    return null;
  }

  // Extract max profit/loss values for the chart
  let maxProfitValue = 0;
  const profitStr = parsedStrategy.profitPotential || parsedStrategy.maxProfit || '';
  const maxProfitMatch = profitStr.match(/\$(\d+(?:\.\d+)?)/);
  if (maxProfitMatch) maxProfitValue = parseFloat(maxProfitMatch[1]);

  let maxLossValue = 0;
  const lossStr = parsedStrategy.maxLoss || '';
  const maxLossMatch = lossStr.match(/\$(\d+(?:\.\d+)?)/);
  if (maxLossMatch) maxLossValue = parseFloat(maxLossMatch[1]);

  // Parse break-even points
  const breakEvenPoints: number[] = [];
  const breakEvenStr = parsedStrategy.breakEven || '';
  const breakEvenMatch = breakEvenStr.match(/\$(\d+(?:\.\d+)?)/g);
  if (breakEvenMatch) {
    breakEvenMatch.forEach(match => {
      const point = parseFloat(match.replace('$', ''));
      if (!isNaN(point)) breakEvenPoints.push(point);
    });
  }

  // Get strategy type for chart color scheme
  const strategyType = parsedStrategy.strategyName.toLowerCase().includes('bull')
    ? 'bullish'
    : parsedStrategy.strategyName.toLowerCase().includes('bear')
      ? 'bearish'
      : 'neutral';

  return (
    <div className="bg-white rounded-lg p-6 shadow-md mb-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <h2 className="text-xl font-bold text-gray-800">
            {parsedStrategy.strategyName}
          </h2>
        </div>

        {/* Toggle button for raw JSON view */}
        <button
          onClick={() => setShowRawJson(!showRawJson)}
          className="px-3 py-1.5 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-all flex items-center"
        >
          <svg className="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
          </svg>
          {showRawJson ? 'Hide Raw JSON' : 'View Raw JSON'}
        </button>
      </div>

      {/* Raw JSON view */}
      {showRawJson && strategyData && (
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 overflow-auto mb-4">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Raw Strategy Data</h3>
          <pre className="text-xs text-gray-600 whitespace-pre-wrap break-all max-h-96 overflow-y-auto">
            {strategyData}
          </pre>
        </div>
      )}

      <div className="mb-6">
        <p className="text-gray-700">{parsedStrategy.overview}</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">Strategy Details</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">Entry Time:</span>
              <span className="font-medium">{parsedStrategy.entryTime}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Expiration:</span>
              <span className="font-medium">{parsedStrategy.expirationTimeframe}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Strike Selection:</span>
              <span className="font-medium">{parsedStrategy.strikeSelection}</span>
            </div>
            {parsedStrategy.impliedVolatility && (
              <div className="flex justify-between">
                <span className="text-gray-600">Implied Volatility:</span>
                <span className="font-medium">{parsedStrategy.impliedVolatility}</span>
              </div>
            )}
            {parsedStrategy.positionSize && (
              <div className="flex justify-between">
                <span className="text-gray-600">Position Size:</span>
                <span className="font-medium">{parsedStrategy.positionSize}</span>
              </div>
            )}
            {parsedStrategy.riskReward && (
              <div className="flex justify-between">
                <span className="text-gray-600">Risk/Reward:</span>
                <span className="font-medium">{parsedStrategy.riskReward}</span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="text-gray-600">Profit Potential:</span>
              <span className="font-medium text-green-600">{parsedStrategy.profitPotential}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Max Loss:</span>
              <span className="font-medium text-red-600">{parsedStrategy.maxLoss}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Break-even:</span>
              <span className="font-medium">{parsedStrategy.breakEven}</span>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Market Environment</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Current Trend:</span>
                <span className="font-medium">{parsedStrategy.marketEnvironment.trend}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Volatility:</span>
                <span className="font-medium">{parsedStrategy.marketEnvironment.volatility}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Sentiment:</span>
                <span className="font-medium">{parsedStrategy.marketEnvironment.sentiment}</span>
              </div>
            </div>
          </div>

          <div className="mb-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Key Indicators</h3>
            <div className="space-y-2">
              {parsedStrategy.indicators.map((indicator, index) => (
                <div key={index} className="mb-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">{indicator.name}:</span>
                    <span className="font-medium">{indicator.condition}</span>
                  </div>
                  <div className="text-xs text-gray-500 mt-1 italic">
                    {indicator.interpretation}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="mb-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Risk Management</h3>
            <div className="space-y-2">
              {parsedStrategy.riskManagement.map((item, index) => (
                <div key={index} className="flex justify-between">
                  <span className="text-gray-600">{item.type}:</span>
                  <span className="font-medium">{item.value}</span>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-3 text-red-700">Position Exit Strategy</h3>
            <div className="space-y-2 bg-red-50 p-3 rounded border border-red-200">
              <div className="mb-2">
                <span className="font-medium text-red-700">When to Close Position:</span>
                <ul className="mt-1 list-disc pl-5 text-sm text-red-700">
                  <li>When profit target is reached (50-75% of max potential)</li>
                  <li>When stop loss is triggered (per risk management)</li>
                  <li>If underlying moves strongly against your position</li>
                  <li>7-10 days before expiration (to avoid theta decay acceleration)</li>
                  <li>When market conditions change contrary to your thesis</li>
                </ul>
              </div>
              <div>
                <span className="font-medium text-red-700">Monitoring Requirements:</span>
                <p className="mt-1 text-sm text-red-700">Track SPY price movements, implied volatility changes, and time decay effects daily. Consider setting price alerts at critical levels.</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <OptionsStrategyBuilder
        suggestedStrategy={strategyType}
        suggestedStrikes={parsedStrategy.strikeSelection}
        initialData={{
          payoffData,
          breakEvenPoints,
          maxProfit: maxProfitValue,
          maxLoss: maxLossValue
        }}
      />

      <RelatedStocksTable stocks={parsedStrategy.relatedStocks} />

      <div className="mt-4 border-t border-gray-200 pt-4">
        <p className="text-sm text-gray-500">
          <strong>Disclaimer:</strong> This options strategy is provided for informational purposes only and does not constitute financial advice. Options trading involves significant risk and is not suitable for all investors. Always conduct your own research and consider consulting with a financial advisor before making investment decisions.
        </p>
      </div>
    </div>
  );
};

export default OptionsStrategy;