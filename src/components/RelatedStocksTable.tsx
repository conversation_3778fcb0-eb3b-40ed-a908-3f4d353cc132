import React from 'react';

export interface RelatedStock {
  symbol: string;
  recommendation: 'Buy' | 'Sell' | 'Hold';
  rationale: string;
  targetPrice?: string;
  outlook?: string;
}

interface RelatedStocksTableProps {
  stocks: RelatedStock[];
}

const RelatedStocksTable: React.FC<RelatedStocksTableProps> = ({ stocks }) => {
  // Sort stocks by recommendation priority (Buy, Hold, Sell)
  const sortedStocks = [...stocks].sort((a, b) => {
    const priority = { 'Buy': 0, 'Hold': 1, 'Sell': 2 };
    return priority[a.recommendation] - priority[b.recommendation];
  });
  
  const getRecommendationColor = (recommendation: string) => {
    switch(recommendation) {
      case 'Buy':
        return 'bg-green-100 text-green-800';
      case 'Sell':
        return 'bg-red-100 text-red-800';
      case 'Hold':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="mt-6">
      <h4 className="text-lg font-medium text-gray-800 mb-2">Related Stocks</h4>
      <div className="overflow-x-auto bg-white rounded-lg border border-gray-200 shadow-sm">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Symbol
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Recommendation
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Outlook
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Rationale
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedStocks.map((stock, index) => (
              <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {stock.symbol}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRecommendationColor(stock.recommendation)}`}>
                    {stock.recommendation}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {stock.outlook || stock.targetPrice || '-'}
                </td>
                <td className="px-6 py-4 text-sm text-gray-500">
                  {stock.rationale}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default RelatedStocksTable;