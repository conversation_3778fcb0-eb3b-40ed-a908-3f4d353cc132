import React, { useState, useRef, useEffect } from 'react';
import apiService from '../services/api';

interface FollowUpQuestionsProps {
  marketPrediction: string;
  optionsStrategy: string;
  newsContext: string;
}

interface QA {
  id: string;
  question: string;
  answer: string;
  isNew?: boolean;
}

const FollowUpQuestions: React.FC<FollowUpQuestionsProps> = ({
  marketPrediction,
  optionsStrategy,
  newsContext
}) => {
  const [question, setQuestion] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [conversations, setConversations] = useState<QA[]>([]);
  const endOfMessagesRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Scroll to the bottom when new messages are added
  useEffect(() => {
    if (endOfMessagesRef.current) {
      endOfMessagesRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [conversations]);

  // Focus the input field when the component loads
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  // Load saved conversations from localStorage
  useEffect(() => {
    try {
      const savedConversations = localStorage.getItem('followUpConversations');
      if (savedConversations) {
        setConversations(JSON.parse(savedConversations));
      }
    } catch (err) {
      console.error('Error loading saved conversations:', err);
    }
  }, []);

  // Save conversations to localStorage when they change
  useEffect(() => {
    if (conversations.length > 0) {
      localStorage.setItem('followUpConversations', JSON.stringify(conversations));
    }
  }, [conversations]);

  // Combine all context for the follow-up question
  const getFullContext = () => {
    return `
MARKET PREDICTION:
${marketPrediction}

OPTIONS STRATEGY:
${optionsStrategy}

NEWS CONTEXT:
${newsContext}
    `;
  };

  // Clear all conversations
  const clearConversations = () => {
    setConversations([]);
    localStorage.removeItem('followUpConversations');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!question.trim() || isLoading) return;

    const currentQuestion = question.trim();
    setQuestion(''); // Clear input immediately for better UX
    setIsLoading(true);
    setError(null);

    // Generate a unique ID for this Q&A pair
    const qaId = `qa-${Date.now()}`;

    // Add the question immediately with a loading placeholder for the answer
    setConversations(prev => [...prev, {
      id: qaId,
      question: currentQuestion,
      answer: '...',
      isNew: true
    }]);

    try {
      const response = await apiService.askFollowUpQuestion(currentQuestion, getFullContext());

      if (response?.choices?.length > 0) {
        const answerContent = response.choices[0].message.content;

        // Update the answer in the conversations array
        setConversations(prev =>
          prev.map(qa =>
            qa.id === qaId
              ? { ...qa, answer: answerContent, isNew: false }
              : qa
          )
        );
      } else {
        throw new Error('Invalid response format from API');
      }
    } catch (err) {
      console.error('Error asking follow-up question:', err);
      setError(err instanceof Error ? err : new Error('Failed to get an answer'));

      // Update the answer to show the error
      setConversations(prev =>
        prev.map(qa =>
          qa.id === qaId
            ? { ...qa, answer: 'Error: Failed to get an answer. Please try again.', isNew: false }
            : qa
        )
      );
    } finally {
      setIsLoading(false);
      // Focus back on the input field
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }
  };

  return (
    <div className="bg-indigo-50 border border-indigo-100 rounded-lg p-4">
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-lg font-semibold text-indigo-800 flex items-center">
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Ask Follow-Up Questions
        </h3>
        {conversations.length > 0 && (
          <button
            onClick={clearConversations}
            className="px-2 py-1 text-xs bg-red-100 hover:bg-red-200 text-red-700 rounded-md transition-all flex items-center"
            title="Clear conversation history"
          >
            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            <span>Clear History</span>
          </button>
        )}
      </div>

      {/* Conversation history */}
      <div className="mb-4 space-y-3 max-h-96 overflow-y-auto p-1 bg-white/50 rounded-lg">
        {conversations.length === 0 ? (
          <div className="text-center text-gray-500 py-6">
            <p>Ask a question about the market analysis or options strategy</p>
            <p className="text-sm mt-2">Examples:</p>
            <ul className="text-sm text-indigo-600 mt-1 space-y-1">
              <li>"What are the key risks to this strategy?"</li>
              <li>"How would this strategy perform in a high volatility environment?"</li>
              <li>"Can you explain the break-even point in more detail?"</li>
            </ul>
          </div>
        ) : (
          conversations.map((qa) => (
            <div
              key={qa.id}
              className={`bg-white rounded-md p-3 shadow-sm transition-all duration-300 ${qa.isNew ? 'border-l-4 border-indigo-500' : ''}`}
            >
              <div className="font-medium text-indigo-700 mb-2">Q: {qa.question}</div>
              <div className="text-gray-700 whitespace-pre-wrap">
                {qa.answer === '...' ? (
                  <div className="flex items-center">
                    <span className="mr-2">Thinking</span>
                    <span className="flex space-x-1">
                      <span className="animate-bounce delay-0 h-2 w-2 bg-indigo-600 rounded-full"></span>
                      <span className="animate-bounce delay-150 h-2 w-2 bg-indigo-600 rounded-full"></span>
                      <span className="animate-bounce delay-300 h-2 w-2 bg-indigo-600 rounded-full"></span>
                    </span>
                  </div>
                ) : (
                  <div className="answer-content">
                    <span className="font-medium text-gray-700">A:</span>
                    <div
                      className="ml-1 inline"
                      dangerouslySetInnerHTML={{
                        __html: qa.answer
                          .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">$1</a>')
                          .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">$1</a>')
                      }}
                    />
                  </div>
                )}
              </div>
            </div>
          ))
        )}
        <div ref={endOfMessagesRef} /> {/* Empty div for scrolling to the end */}
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 text-red-700 p-2 rounded mb-3">
          Error: {error.message}
        </div>
      )}

      {/* Question form */}
      <form onSubmit={handleSubmit} className="flex items-center">
        <input
          ref={inputRef}
          type="text"
          value={question}
          onChange={(e) => setQuestion(e.target.value)}
          placeholder="Type your question here..."
          className="flex-grow p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          disabled={isLoading}
        />
        <button
          type="submit"
          className={`bg-indigo-600 text-white px-4 py-2 rounded-r-md ${
            isLoading ? 'opacity-70 cursor-not-allowed' : 'hover:bg-indigo-700'
          }`}
          disabled={isLoading}
        >
          {isLoading ? (
            <svg className="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          ) : (
            'Ask'
          )}
        </button>
      </form>
    </div>
  );
};

export default FollowUpQuestions;
