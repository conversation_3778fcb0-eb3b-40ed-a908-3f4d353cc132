import React, { useState, useEffect } from 'react';

interface MarkdownLinkDetectorProps {
  content: string;
  onLinkClick: (url: string) => void;
}

/**
 * Component that detects and renders markdown links as clickable elements
 */
const MarkdownLinkDetector: React.FC<MarkdownLinkDetectorProps> = ({ content, onLinkClick }) => {
  const [processedContent, setProcessedContent] = useState<string>(content);

  useEffect(() => {
    // Process the content to handle markdown links
    let processed = content;
    
    // Replace markdown links with clickable spans
    processed = processed.replace(/\[([^\]]+)\]\(([^)]+)\)/g, (match, text, url) => {
      return `<a href="${url}" class="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">${text}</a>`;
    });
    
    // Remove other markdown formatting
    processed = processed.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>'); // Bold
    processed = processed.replace(/\*([^*]+)\*/g, '<em>$1</em>'); // Italic
    
    setProcessedContent(processed);
  }, [content]);

  const handleClick = (e: React.MouseEvent) => {
    // Handle clicks on links
    if ((e.target as HTMLElement).tagName === 'A') {
      e.preventDefault();
      const url = (e.target as HTMLAnchorElement).href;
      onLinkClick(url);
    }
  };

  return (
    <div 
      className="markdown-content"
      onClick={handleClick}
      dangerouslySetInnerHTML={{ __html: processedContent }}
    />
  );
};

export default MarkdownLinkDetector;
