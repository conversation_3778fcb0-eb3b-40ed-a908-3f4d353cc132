import React, { useState, useEffect } from 'react';
import apiService from '../services/api';

interface MarketPredictionProps {
  newsItems: string;
  onPredictionSave: (prediction: string) => void;
  isAutoAnalyzing?: boolean;
}

const MarketPrediction: React.FC<MarketPredictionProps> = ({ 
  newsItems, 
  onPredictionSave,
  isAutoAnalyzing = false
}) => {
  const [prediction, setPrediction] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [rawPredictionData, setRawPredictionData] = useState<string>('');
  
  // Auto-analyze on load if specified
  useEffect(() => {
    if (isAutoAnalyzing && newsItems) {
      handlePredictMarket();
    }
  }, [isAutoAnalyzing, newsItems]);

  // Predict market based on news items
  const handlePredictMarket = async () => {
    if (!newsItems || isLoading) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await apiService.predictMarketOpen(newsItems);
      
      if (response?.choices?.length > 0) {
        const predictionContent = response.choices[0].message.content;
        setRawPredictionData(predictionContent);
        
        // Parse prediction content
        const parsed = parsePredictionContent(predictionContent);
        setPrediction(parsed);
      } else {
        throw new Error('Invalid response format from API');
      }
    } catch (err) {
      console.error('Error predicting market:', err);
      setError(err instanceof Error ? err : new Error('Failed to generate market prediction'));
    } finally {
      setIsLoading(false);
    }
  };
  
  // Parse prediction content from API response
  const parsePredictionContent = (content: string) => {
    // Example parsing logic - adjust based on actual response format
    const predictionMatch = content.match(/PREDICTION:\s*([A-Za-z]+)/i);
    const expectedOpenMatch = content.match(/EXPECTED SPY OPEN:\s*([^\\n]+)/i);
    const confidenceMatch = content.match(/CONFIDENCE:\s*([A-Za-z]+)/i);
    
    const analysisMatch = content.match(/ANALYSIS:([\s\S]+?)(?=KEY FACTORS:|$)/i);
    const keyFactorsMatch = content.match(/KEY FACTORS:([\s\S]+?)(?=RISKS TO PREDICTION:|$)/i);
    const risksMatch = content.match(/RISKS TO PREDICTION:([\s\S]+?)(?=$)/i);
    
    const keyFactors = keyFactorsMatch ? 
      keyFactorsMatch[1].split('-').filter(f => f.trim()).map(f => f.trim()) : 
      [];
      
    const risks = risksMatch ? 
      risksMatch[1].split('-').filter(r => r.trim()).map(r => r.trim()) : 
      [];
    
    return {
      prediction: predictionMatch ? predictionMatch[1] : 'Unknown',
      expectedOpen: expectedOpenMatch ? expectedOpenMatch[1] : 'Unknown',
      confidence: confidenceMatch ? confidenceMatch[1] : 'Medium',
      analysis: analysisMatch ? analysisMatch[1].trim() : '',
      keyFactors,
      risks,
      raw: content
    };
  };
  
  // Save prediction data
  const handleSavePrediction = () => {
    if (rawPredictionData) {
      onPredictionSave(rawPredictionData);
    }
  };
  
  // Get color classes based on prediction
  const getPredictionColor = () => {
    if (!prediction) return 'text-gray-600';
    
    switch (prediction.prediction.toLowerCase()) {
      case 'bullish':
        return 'text-green-600';
      case 'bearish':
        return 'text-red-600';
      default:
        return 'text-yellow-600';
    }
  };
  
  // Get color classes based on confidence level
  const getConfidenceColor = () => {
    if (!prediction) return 'bg-gray-400';
    
    switch (prediction.confidence.toLowerCase()) {
      case 'high':
        return 'bg-green-600';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-red-500';
      default:
        return 'bg-blue-500';
    }
  };
  
  // Loading state
  if (isLoading || isAutoAnalyzing) {
    return (
      <div className="bg-white rounded-lg p-6 shadow animate-pulse">
        <div className="h-8 bg-gray-200 rounded-lg w-2/3 mb-4"></div>
        <div className="space-y-3">
          <div className="h-5 bg-gray-200 rounded w-full"></div>
          <div className="h-5 bg-gray-200 rounded w-5/6"></div>
          <div className="h-5 bg-gray-200 rounded w-4/6"></div>
        </div>
        <div className="mt-6 h-10 bg-gray-200 rounded-lg w-1/3"></div>
      </div>
    );
  }
  
  // Error state
  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-red-700 mb-2">Error Analyzing Market</h3>
        <p className="text-red-600">{error.message}</p>
        <button
          onClick={handlePredictMarket}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          Retry Analysis
        </button>
      </div>
    );
  }
  
  // No prediction yet state
  if (!prediction) {
    return (
      <div className="bg-white rounded-lg p-6 shadow">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Market Analysis</h3>
        <p className="text-gray-600 mb-6">
          Analyze the market based on the latest news to generate a prediction for the next trading day.
        </p>
        <button
          onClick={handlePredictMarket}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center transition-all"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          Analyze Market
        </button>
      </div>
    );
  }
  
  // Prediction display
  return (
    <div className="bg-white rounded-lg p-6 shadow-md animate-fade-in">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
        <h2 className="text-xl font-bold flex items-center text-gray-800">
          <svg className="w-6 h-6 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          SPY Next Trading Day Prediction
        </h2>
        <div className="flex items-center space-x-2">
          <span className={`text-lg font-bold ${getPredictionColor()}`}>
            {prediction.prediction}
          </span>
          <span className="text-gray-500">•</span>
          <span className="text-lg font-semibold">
            {prediction.expectedOpen}
          </span>
          <span className="text-gray-500">•</span>
          <div className="flex items-center">
            <span className="text-sm text-gray-600 mr-2">Confidence:</span>
            <span className={`${getConfidenceColor()} text-white text-xs font-medium px-2 py-0.5 rounded-full`}>
              {prediction.confidence}
            </span>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-700 mb-2">Analysis</h3>
        <div className="text-gray-600 bg-gray-50 rounded-lg p-4 border border-gray-100">
          <p className="whitespace-pre-wrap">{prediction.analysis}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
          <h3 className="text-md font-semibold text-blue-800 mb-2">Key Factors</h3>
          <ul className="list-disc list-inside text-blue-700 space-y-2">
            {prediction.keyFactors.map((factor: string, index: number) => (
              <li key={index} className="text-blue-700">{factor}</li>
            ))}
          </ul>
        </div>
        <div className="bg-red-50 rounded-lg p-4 border border-red-100">
          <h3 className="text-md font-semibold text-red-800 mb-2">Risks to Prediction</h3>
          <ul className="list-disc list-inside text-red-700 space-y-2">
            {prediction.risks.map((risk: string, index: number) => (
              <li key={index} className="text-red-700">{risk}</li>
            ))}
          </ul>
        </div>
      </div>
      
      <div className="flex justify-end">
        <button
          onClick={handleSavePrediction}
          className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 flex items-center transition-all shadow-md"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
          </svg>
          Continue to Strategy
        </button>
      </div>
    </div>
  );
};

export default MarketPrediction;