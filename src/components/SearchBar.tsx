import React, { useState } from 'react';

interface SearchBarProps {
  onSearch: (query: string) => void;
  isLoading: boolean;
}

const SearchBar: React.FC<SearchBarProps> = ({ onSearch, isLoading }) => {
  const [query, setQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      onSearch(query);
    }
  };

  const handlePresetQuery = (presetQuery: string) => {
    if (presetQuery.trim()) {
      setQuery(presetQuery);
      onSearch(presetQuery);
    }
  };

  // Preset queries for common market-moving factors
  const presetQueries = [
    { label: 'Fed News', query: 'Latest Federal Reserve news, interest rates, and policy decisions that could impact S&P 500' },
    { label: 'Earnings', query: 'Major earnings reports or surprises from S&P 500 companies today and their market impact' },
    { label: 'Economic Data', query: 'Recent economic data releases that could move the S&P 500 market today (GDP, employment, inflation)' },
    { label: 'Geopolitical', query: 'Major geopolitical events and news affecting global markets and the S&P 500' },
  ];

  return (
    <div className="bg-white rounded-lg shadow-md p-4 mb-6 transition-all duration-300 hover:shadow-lg">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="query" className="block text-sm font-medium text-gray-700 mb-1">
            Customize News Query
          </label>
          <div className={`flex ${isFocused ? 'ring-2 ring-blue-300 rounded-md' : ''}`}>
            <input
              type="text"
              id="query"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              placeholder="Enter a specific news query for SPY market impact..."
              className="flex-1 min-w-0 block w-full px-4 py-2 border border-gray-300 rounded-l-md focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
              disabled={isLoading}
            />
            <button
              type="submit"
              disabled={isLoading || !query.trim()}
              className={`inline-flex items-center px-4 py-2 border border-transparent font-medium rounded-r-md text-white transition-all duration-300 ${
                isLoading || !query.trim()
                  ? 'bg-blue-300 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:shadow-md transform hover:-translate-y-px'
              }`}
            >
              {isLoading ? (
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                'Search'
              )}
            </button>
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-2">Quick Searches</h3>
          <div className="flex flex-wrap gap-2">
            {presetQueries.map((preset, index) => (
              <button
                key={index}
                type="button"
                onClick={() => handlePresetQuery(preset.query)}
                disabled={isLoading}
                className={`inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 transition-all duration-300 ${
                  isLoading
                    ? 'bg-gray-100 cursor-not-allowed'
                    : 'bg-white hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-blue-500 transform hover:-translate-y-1 hover:shadow-sm'
                }`}
              >
                {preset.label}
              </button>
            ))}
          </div>
        </div>
      </form>
    </div>
  );
};

export default SearchBar; 