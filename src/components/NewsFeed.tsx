import React, { useState } from 'react';
import NewsItem from './NewsItem';
import MarketPrediction from './MarketPrediction';
import apiService from '../services/api';
import { parseMarketPrediction, MarketPrediction as MarketPredictionType } from '../utils/newsParser';

interface NewsData {
  title: string;
  content: string;
  source?: string;
  timestamp?: string;
  impact?: 'high' | 'medium' | 'low';
  url?: string;
}

interface NewsFeedProps {
  news: NewsData[];
  isLoading: boolean;
  error: Error | null;
  onRefresh: () => void;
}

const NewsFeed: React.FC<NewsFeedProps> = ({ news, isLoading, error, onRefresh }) => {
  const [filter, setFilter] = useState<'all' | 'high' | 'medium' | 'low'>('all');
  const [animateRefresh, setAnimateRefresh] = useState(false);
  const [marketPrediction, setMarketPrediction] = useState<MarketPredictionType | null>(null);
  const [isPredictionLoading, setIsPredictionLoading] = useState(false);
  const [predictionError, setPredictionError] = useState<Error | null>(null);

  // Filter out items that appear to be just URLs or source references
  const validNewsItems = news.filter(item => {
    // Skip items with titles that are just URLs or source references
    const isTitleUrl = item.title.match(/^(?:URL|SOURCE)\s*:\s*/i) ||
                       item.title.match(/^https?:\/\//i);

    // Skip items that are very short and contain URLs
    const isJustUrl = item.title.length < 30 && item.url &&
                     (item.content.includes(item.url) || !item.content);

    return !isTitleUrl && !isJustUrl;
  }).map(item => {
    // Process content to ensure it's not duplicate of title
    let processedContent = item.content;

    // Remove title from beginning of content if it's duplicated
    if (processedContent.startsWith(item.title)) {
      processedContent = processedContent.substring(item.title.length).trim();
    }

    // Remove any leading punctuation that might be left after stripping the title
    processedContent = processedContent.replace(/^[.,;:\s]+/, '');

    // If content is very short or empty after processing, use a fallback
    if (processedContent.length < 15) {
      processedContent = "Click to read the full article.";
    }

    return {
      ...item,
      content: processedContent
    };
  });

  const filteredNews = filter === 'all'
    ? validNewsItems
    : validNewsItems.filter(item => item.impact === filter);

  const handleRefresh = () => {
    setAnimateRefresh(true);
    onRefresh();
    setTimeout(() => setAnimateRefresh(false), 1000);
  };

  const handlePredictMarket = async () => {
    if (news.length === 0) return;

    setIsPredictionLoading(true);
    setPredictionError(null);
    setMarketPrediction(null);

    try {
      // Prepare news items for the prediction request
      const newsContent = news.map(item => {
        return `HEADLINE: ${item.title}
SOURCE: ${item.source || 'Unknown'}
IMPACT: ${item.impact}
${item.content}
${item.url ? `URL: ${item.url}` : ''}

`;
      }).join('\n');

      // Make the prediction request
      const response = await apiService.predictMarketOpen(newsContent);

      // Parse the response
      const prediction = parseMarketPrediction(response);
      setMarketPrediction(prediction);
    } catch (err) {
      console.error('Error fetching market prediction:', err);
      setPredictionError(err instanceof Error ? err : new Error('Failed to get market prediction'));
    } finally {
      setIsPredictionLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[300px] bg-white rounded-lg p-8 shadow-md">
        <div className="animate-spin rounded-full h-14 w-14 border-t-2 border-b-2 border-blue-600 mb-5"></div>
        <p className="text-gray-700 font-medium text-center">Fetching latest market-moving news...</p>
        <div className="mt-8 w-full max-w-md space-y-3">
          <div className="h-2.5 bg-gray-200 rounded-full w-full animate-pulse"></div>
          <div className="h-2.5 bg-gray-200 rounded-full w-3/4 animate-pulse"></div>
          <div className="h-2.5 bg-gray-200 rounded-full w-1/2 animate-pulse"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8 bg-red-50 rounded-lg border border-red-200 text-red-700 transition-all duration-300 hover:bg-red-100">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 text-red-500 mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 className="text-xl font-bold mb-2">Error Loading News</h3>
        <p className="mb-5 text-red-600">{error.message}</p>
        <button
          onClick={onRefresh}
          className="bg-red-600 hover:bg-red-700 text-white font-medium py-2.5 px-5 rounded-lg transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1 inline-flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Try Again
        </button>
      </div>
    );
  }

  if (validNewsItems.length === 0) {
    return (
      <div className="text-center p-8 bg-gray-50 rounded-lg shadow-sm transition-all duration-300 hover:bg-gray-100">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-200 text-gray-500 mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1M19 20a2 2 0 002-2V8a2 2 0 00-2-2h-1" />
          </svg>
        </div>
        <p className="text-gray-600 text-lg mb-5">No news articles found</p>
        <button
          onClick={onRefresh}
          className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-5 rounded-lg transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1 inline-flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Refresh News
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-5">
      <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
        <div className="flex flex-col gap-4">
          <h2 className="text-2xl font-bold text-gray-800 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1M19 20a2 2 0 002-2V8a2 2 0 00-2-2h-1M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
            Market Impact News
          </h2>

          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div className="bg-gray-100 p-1 rounded-xl flex flex-wrap" role="group">
              <button
                type="button"
                onClick={() => setFilter('all')}
                className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                  filter === 'all'
                    ? 'bg-blue-600 text-white shadow-md'
                    : 'text-gray-700 hover:bg-gray-200'
                }`}
              >
                All
              </button>
              <button
                type="button"
                onClick={() => setFilter('high')}
                className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                  filter === 'high'
                    ? 'bg-red-600 text-white shadow-md'
                    : 'text-gray-700 hover:bg-gray-200'
                }`}
              >
                High Impact
              </button>
              <button
                type="button"
                onClick={() => setFilter('medium')}
                className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                  filter === 'medium'
                    ? 'bg-yellow-600 text-white shadow-md'
                    : 'text-gray-700 hover:bg-gray-200'
                }`}
              >
                Medium Impact
              </button>
              <button
                type="button"
                onClick={() => setFilter('low')}
                className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                  filter === 'low'
                    ? 'bg-green-600 text-white shadow-md'
                    : 'text-gray-700 hover:bg-gray-200'
                }`}
              >
                Low Impact
              </button>
            </div>

            <div className="text-sm text-gray-500 flex items-center">
              Showing {filteredNews.length} {filter !== 'all' ? filter + ' impact' : ''} news items
            </div>
          </div>
        </div>
      </div>

      {marketPrediction && (
        <MarketPrediction
          prediction={marketPrediction}
          isLoading={isPredictionLoading}
          error={predictionError}
          newsContext={news.map(item => {
            return `HEADLINE: ${item.title}
SOURCE: ${item.source || 'Unknown'}
IMPACT: ${item.impact}
${item.content}
${item.url ? `URL: ${item.url}` : ''}

`;
          }).join('\n')}
        />
      )}

      <div className="space-y-5">
        {filteredNews.map((item, index) => (
          <div
            key={index}
            className="animate-fadeIn transition-all duration-500 hover:scale-[1.01]"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <NewsItem
              title={item.title}
              content={item.content}
              source={item.source}
              timestamp={item.timestamp}
              impact={item.impact}
              url={item.url}
            />
          </div>
        ))}
      </div>

      {filteredNews.length === 0 && filter !== 'all' && (
        <div className="text-center p-6 bg-gray-50 rounded-lg">
          <p className="text-gray-600">No {filter} impact news found. Try another filter.</p>
        </div>
      )}
    </div>
  );
};

export default NewsFeed;