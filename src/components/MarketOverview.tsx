import React from 'react';

interface MarketOverviewProps {
  spyPrice?: number;
  spyChange?: number;
  spyChangePercent?: number;
  volume?: number;
  lastUpdate?: string;
  isLoading: boolean;
  isError: boolean;
  onManualRefresh: () => void;
  isCompact?: boolean; // Add a prop to control compact display
}

const MarketOverview: React.FC<MarketOverviewProps> = ({
  spyPrice,
  spyChange,
  spyChangePercent,
  volume,
  lastUpdate,
  isLoading,
  isError,
  onManualRefresh,
  isCompact = false // Default to full version
}) => {
  // Format the price with two decimal places
  const formattedPrice = spyPrice ? spyPrice.toFixed(2) : '--';
  
  // Format the change with two decimal places and sign
  const formattedChange = spyChange 
    ? `${spyChange > 0 ? '+' : ''}${spyChange.toFixed(2)}` 
    : '--';
  
  // Format the percent change with two decimal places and sign
  const formattedPercentChange = spyChangePercent 
    ? `${spyChangePercent > 0 ? '+' : ''}${spyChangePercent.toFixed(2)}%` 
    : '--';
  
  // Format volume with commas
  const formattedVolume = volume 
    ? volume.toLocaleString() 
    : '--';
  
  // Get the color class based on the price change
  const getChangeColorClass = () => {
    if (!spyChange) return 'text-gray-400';
    return spyChange > 0 ? 'text-green-600' : spyChange < 0 ? 'text-red-600' : 'text-gray-600';
  };
  
  // Render a compact version when specified via props
  return isCompact ? (
    // Compact version for sticky header
    <div className="flex items-center justify-between">
      <div className="flex items-center">
        <span className="font-bold mr-2 text-lg">SPY</span>
        <span className="text-xl font-bold mr-2">${formattedPrice}</span>
        <span className={`text-sm font-medium ${getChangeColorClass()}`}>
          {formattedChange} ({formattedPercentChange})
        </span>
      </div>
      
      <div className="flex items-center space-x-4">
        <div className="hidden sm:block">
          <span className="text-xs text-gray-500">Vol: </span>
          <span className="text-xs font-medium">{volume ? (volume / 1000000).toFixed(1) + 'M' : '--'}</span>
        </div>
        
        <div className="hidden sm:block">
          <span className="text-xs text-gray-500">Updated: </span>
          <span className="text-xs font-medium">{lastUpdate ? lastUpdate.split(' ')[1] : '--'}</span>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={onManualRefresh}
            disabled={isLoading}
            className="p-1 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100 transition-colors"
            title="Refresh Data"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          </button>
          <span className="px-2 py-0.5 text-xs rounded-full bg-blue-100 text-blue-700">
            Live
          </span>
        </div>
      </div>
    </div>
  ) : (
    // Full version for main content
    <div className="bg-white shadow-md rounded-lg p-4 mb-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-gray-700">SPY Market Data</h2>
        <div className="flex items-center space-x-2">
          <button
            onClick={onManualRefresh}
            disabled={isLoading}
            className="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100 transition-colors"
            title="Reconnect WebSocket"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          </button>
          <span className="px-3 py-1 text-xs rounded-full bg-blue-100 text-blue-700">
            Real-Time
          </span>
        </div>
      </div>

      {isError ? (
        <div className="p-4 bg-red-50 text-red-700 rounded-md">
          <p className="font-medium">Error loading market data</p>
          <p className="text-sm mt-1">Please try reconnecting or check your connection.</p>
        </div>
      ) : isLoading && !spyPrice ? (
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-2 gap-4">
            <div className="h-6 bg-gray-200 rounded"></div>
            <div className="h-6 bg-gray-200 rounded"></div>
            <div className="h-6 bg-gray-200 rounded"></div>
            <div className="h-6 bg-gray-200 rounded"></div>
          </div>
        </div>
      ) : (
        <>
          <div className="mb-4">
            <span className="text-3xl font-bold">${formattedPrice}</span>
            <span className={`ml-2 text-lg font-semibold ${getChangeColorClass()}`}>
              {formattedChange} ({formattedPercentChange})
            </span>
          </div>
          
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span className="text-gray-500">Volume:</span>
              <span className="ml-2 font-medium">{formattedVolume}</span>
            </div>
            <div>
              <span className="text-gray-500">Last Update:</span>
              <span className="ml-2 font-medium">{lastUpdate || '--'}</span>
            </div>
          </div>
          
          <div className="mt-3 p-2 bg-blue-50 rounded-md border border-blue-100 text-xs text-blue-700">
            <p className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Real-time WebSocket data stream. Click refresh to reconnect if needed.
            </p>
          </div>
        </>
      )}
    </div>
  );
};

export default MarketOverview; 