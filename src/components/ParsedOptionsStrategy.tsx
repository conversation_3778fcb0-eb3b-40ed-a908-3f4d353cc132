import React from 'react';

interface ParsedOptionsStrategyProps {
  rawData: string;
}

const ParsedOptionsStrategy: React.FC<ParsedOptionsStrategyProps> = ({ rawData }) => {
  // Parse strategy data from API response
  const parseStrategyData = (content: string) => {
    try {
      // First, try to parse as JSON
      try {
        // Check if the content is a JSON string
        if (content.trim().startsWith('{') && content.trim().endsWith('}')) {
          const jsonData = JSON.parse(content);
          // Only log in development mode
          if (process.env.NODE_ENV === 'development') {
            console.log("[ParsedOptionsStrategy] Parsed JSON data (once):", jsonData);
          }

          // Map JSON fields to our expected structure
          return {
            strategyName: jsonData.strategy_name || jsonData.strategyName || 'Options Strategy',
            overview: jsonData.overview || '',
            entryTime: jsonData.entry_time || jsonData.entryTime || '',
            expirationTimeframe: jsonData.expiration_timeframe || jsonData.expirationTimeframe || '',
            strikeSelection: jsonData.strike_selection || jsonData.strikeSelection || '',
            impliedVolatility: jsonData.implied_volatility || jsonData.impliedVolatility || '',
            positionSize: jsonData.position_size || jsonData.positionSize || '',
            riskReward: jsonData.risk_reward || jsonData.riskReward || '',
            profitPotential: jsonData.profit_potential || jsonData.profitPotential || '',
            maxLoss: jsonData.max_loss || jsonData.maxLoss || '',
            breakEven: jsonData.break_even || jsonData.breakEven || '',
            currentTrend: jsonData.current_trend || jsonData.currentTrend ||
                         (jsonData.marketEnvironment?.trend) || '',
            volatilityEnvironment: jsonData.volatility_environment || jsonData.volatilityEnvironment ||
                                  (jsonData.marketEnvironment?.volatility) || '',
            sentiment: jsonData.sentiment || (jsonData.marketEnvironment?.sentiment) || '',
            stopLoss: jsonData.stop_loss || jsonData.stopLoss ||
                     (jsonData.riskManagement?.stopLoss) || '',
            takeProfit: jsonData.take_profit || jsonData.takeProfit ||
                       (jsonData.riskManagement?.takeProfit) || '',
            adjustmentCriteria: jsonData.adjustment_criteria || jsonData.adjustmentCriteria ||
                               (jsonData.riskManagement?.adjustmentCriteria) || '',
            relatedStocks: jsonData.related_stocks || jsonData.relatedStocks || []
          };
        }
      } catch (jsonError) {
        // Only log in development mode
        if (process.env.NODE_ENV === 'development') {
          console.log("[ParsedOptionsStrategy] Not valid JSON, continuing with regex parsing");
        }
      }

      // If not JSON, use regex parsing with more flexible patterns
      // Strategy name - look for various formats
      const strategyNamePatterns = [
        /STRATEGY[_\s]?NAME:\s*([^\n]+)/i,
        /STRATEGY:\s*([^\n]+)/i,
        /RECOMMENDED\s+STRATEGY:\s*([^\n]+)/i,
        /^([^\n:]+\s+Strategy)\s*$/im,  // Line that ends with "Strategy"
      ];

      let strategyName = '';
      for (const pattern of strategyNamePatterns) {
        const match = content.match(pattern);
        if (match) {
          strategyName = match[1].trim();
          break;
        }
      }

      // Overview - try to capture multi-line text
      const overviewMatch = content.match(/OVERVIEW:\s*([\s\S]+?)(?=\n\s*[A-Z_]+:|$)/i) ||
                           content.match(/DESCRIPTION:\s*([\s\S]+?)(?=\n\s*[A-Z_]+:|$)/i);

      // Strategy details with multiple possible field names
      const entryTimeMatch = content.match(/ENTRY[_\s]?TIME:\s*([^\n]+)/i) ||
                            content.match(/ENTRY:\s*([^\n]+)/i);

      const expirationMatch = content.match(/EXPIRATION[_\s]?TIMEFRAME:\s*([^\n]+)/i) ||
                             content.match(/EXPIRATION:\s*([^\n]+)/i) ||
                             content.match(/EXPIRY:\s*([^\n]+)/i);

      const strikeMatch = content.match(/STRIKE[_\s]?SELECTION:\s*([^\n]+)/i) ||
                         content.match(/STRIKE:\s*([^\n]+)/i);

      const ivMatch = content.match(/IMPLIED[_\s]?VOLATILITY:\s*([^\n]+)/i) ||
                     content.match(/IV:\s*([^\n]+)/i);

      const positionMatch = content.match(/POSITION[_\s]?SIZE:\s*([^\n]+)/i) ||
                          content.match(/POSITION:\s*([^\n]+)/i);

      const riskMatch = content.match(/RISK[_\s]?REWARD:\s*([^\n]+)/i) ||
                      content.match(/RISK\/REWARD:\s*([^\n]+)/i);

      const profitMatch = content.match(/PROFIT[_\s]?POTENTIAL:\s*([^\n]+)/i) ||
                        content.match(/PROFIT:\s*([^\n]+)/i);

      const maxLossMatch = content.match(/MAX[_\s]?LOSS:\s*([^\n]+)/i) ||
                         content.match(/MAXIMUM[_\s]?LOSS:\s*([^\n]+)/i);

      const breakEvenMatch = content.match(/BREAK[_\s]?EVEN:\s*([^\n]+)/i);

      // Market environment section
      const trendMatch = content.match(/CURRENT[_\s]?TREND:\s*([^\n]+)/i) ||
                       content.match(/TREND:\s*([^\n]+)/i);

      const volMatch = content.match(/VOLATILITY[_\s]?ENVIRONMENT:\s*([^\n]+)/i) ||
                     content.match(/VOLATILITY:\s*([^\n]+)/i);

      const sentimentMatch = content.match(/SENTIMENT:\s*([^\n]+)/i) ||
                           content.match(/MARKET[_\s]?SENTIMENT:\s*([^\n]+)/i);

      // Risk management
      const stopLossMatch = content.match(/STOP[_\s]?LOSS:\s*([^\n]+)/i);
      const takeProfitMatch = content.match(/TAKE[_\s]?PROFIT:\s*([^\n]+)/i);
      const adjustmentMatch = content.match(/ADJUSTMENT[_\s]?CRITERIA:\s*([^\n]+)/i) ||
                             content.match(/ADJUSTMENTS:\s*([^\n]+)/i);

      // Related stocks - extract all
      const relatedStocks = [];

      // Try to find related stocks section
      const relatedStocksSection = content.match(/RELATED[_\s]?STOCKS:[\s\S]+?(?=\n\s*[A-Z_]+:|$)/i);

      if (relatedStocksSection) {
        // Extract individual stocks
        const stockMatches = relatedStocksSection[0].matchAll(/([A-Z]{1,5})\s*-\s*([^\n]+)/g);
        for (const match of stockMatches) {
          relatedStocks.push({
            symbol: match[1].trim(),
            recommendation: match[2].includes('Buy') ? 'Buy' :
                           match[2].includes('Sell') ? 'Sell' : 'Hold',
            rationale: match[2].trim(),
            outlook: ''
          });
        }
      }

      // If no stocks found with the above method, try the numbered approach
      if (relatedStocks.length === 0) {
        for (let i = 1; i <= 5; i++) {
          const symbolMatch = content.match(new RegExp(`RELATED[_\\s]?STOCK[_\\s]?${i}[_\\s]?SYMBOL:\\s*([^\\n]+)`, 'i')) ||
                             content.match(new RegExp(`STOCK[_\\s]?${i}:\\s*([^\\n]+)`, 'i'));

          const recMatch = content.match(new RegExp(`RELATED[_\\s]?STOCK[_\\s]?${i}[_\\s]?RECOMMENDATION:\\s*([^\\n]+)`, 'i')) ||
                         content.match(new RegExp(`STOCK[_\\s]?${i}[_\\s]?REC:\\s*([^\\n]+)`, 'i'));

          const rationaleMatch = content.match(new RegExp(`RELATED[_\\s]?STOCK[_\\s]?${i}[_\\s]?RATIONALE:\\s*([^\\n]+)`, 'i')) ||
                              content.match(new RegExp(`STOCK[_\\s]?${i}[_\\s]?REASON:\\s*([^\\n]+)`, 'i'));

          if (symbolMatch) {
            relatedStocks.push({
              symbol: symbolMatch[1].trim(),
              recommendation: recMatch ? recMatch[1].trim() : '',
              rationale: rationaleMatch ? rationaleMatch[1].trim() : '',
              outlook: ''
            });
          }
        }
      }

      return {
        strategyName: strategyName || 'Options Strategy',
        overview: overviewMatch ? overviewMatch[1].trim() : '',
        entryTime: entryTimeMatch ? entryTimeMatch[1].trim() : '',
        expirationTimeframe: expirationMatch ? expirationMatch[1].trim() : '',
        strikeSelection: strikeMatch ? strikeMatch[1].trim() : '',
        impliedVolatility: ivMatch ? ivMatch[1].trim() : '',
        positionSize: positionMatch ? positionMatch[1].trim() : '',
        riskReward: riskMatch ? riskMatch[1].trim() : '',
        profitPotential: profitMatch ? profitMatch[1].trim() : '',
        maxLoss: maxLossMatch ? maxLossMatch[1].trim() : '',
        breakEven: breakEvenMatch ? breakEvenMatch[1].trim() : '',
        currentTrend: trendMatch ? trendMatch[1].trim() : '',
        volatilityEnvironment: volMatch ? volMatch[1].trim() : '',
        sentiment: sentimentMatch ? sentimentMatch[1].trim() : '',
        stopLoss: stopLossMatch ? stopLossMatch[1].trim() : '',
        takeProfit: takeProfitMatch ? takeProfitMatch[1].trim() : '',
        adjustmentCriteria: adjustmentMatch ? adjustmentMatch[1].trim() : '',
        relatedStocks
      };
    } catch (error) {
      console.error("Error parsing strategy data:", error);
      return null;
    }
  };

  // Parse the raw data
  console.log("Raw options strategy data:", rawData);

  // Try to parse as JSON first
  let strategy;
  try {
    // First, try to extract JSON from the raw data if it's embedded
    let jsonContent = rawData;

    // Look for JSON object pattern in the raw data
    const jsonMatch = rawData.match(/\{[\s\S]*\}/m);
    if (jsonMatch) {
      jsonContent = jsonMatch[0];
      console.log("Extracted JSON from raw data:", jsonContent);
    }

    if (jsonContent.trim().startsWith('{') && jsonContent.trim().endsWith('}')) {
      const jsonData = JSON.parse(jsonContent);
      // Only log in development mode
      if (process.env.NODE_ENV === 'development') {
        console.log("[ParsedOptionsStrategy] Directly parsed JSON strategy (once):", jsonData);
      }

      // Map the JSON fields to our expected format
      strategy = {
        strategyName: jsonData.strategy_name || jsonData.strategyName || 'Unknown Strategy',
        overview: jsonData.overview || '',
        entryTime: jsonData.entry_time || jsonData.entryTime || '',
        expirationTimeframe: jsonData.expiration_timeframe || jsonData.expirationTimeframe || '',
        strikeSelection: jsonData.strike_selection || jsonData.strikeSelection || '',
        impliedVolatility: jsonData.implied_volatility || jsonData.impliedVolatility || '',
        positionSize: jsonData.position_size || jsonData.positionSize || '',
        riskReward: jsonData.risk_reward || jsonData.riskReward || '',
        profitPotential: jsonData.profit_potential || jsonData.profitPotential || '',
        maxLoss: jsonData.max_loss || jsonData.maxLoss || '',
        breakEven: jsonData.break_even || jsonData.breakEven || '',
        currentTrend: jsonData.current_trend || (jsonData.marketEnvironment && jsonData.marketEnvironment.trend) || '',
        volatilityEnvironment: jsonData.volatility_environment || (jsonData.marketEnvironment && jsonData.marketEnvironment.volatility) || '',
        sentiment: jsonData.sentiment || (jsonData.marketEnvironment && jsonData.marketEnvironment.sentiment) || '',
        stopLoss: jsonData.stop_loss || (jsonData.riskManagement && jsonData.riskManagement.stopLoss) || '',
        takeProfit: jsonData.take_profit || (jsonData.riskManagement && jsonData.riskManagement.takeProfit) || '',
        adjustmentCriteria: jsonData.adjustment_criteria || (jsonData.riskManagement && jsonData.riskManagement.adjustmentCriteria) || '',
        relatedStocks: Array.isArray(jsonData.related_stocks) ? jsonData.related_stocks.map(stock => ({
          symbol: stock.symbol || '',
          recommendation: stock.recommendation || '',
          rationale: stock.rationale || '',
          outlook: stock.outlook || ''
        })) : (Array.isArray(jsonData.relatedStocks) ? jsonData.relatedStocks.map(stock => ({
          symbol: stock.symbol || '',
          recommendation: stock.recommendation || '',
          rationale: stock.rationale || '',
          outlook: stock.outlook || ''
        })) : [])
      };
    } else {
      strategy = parseStrategyData(rawData);
    }
  } catch (error) {
    console.error("Error parsing JSON, falling back to regex:", error);
    strategy = parseStrategyData(rawData);
  }

  if (!strategy) {
    return <div className="text-red-600">Error parsing strategy data</div>;
  }

  // Only log in development mode
  if (process.env.NODE_ENV === 'development') {
    console.log("[ParsedOptionsStrategy] Final parsed strategy data (once):", strategy);
  }

  return (
    <div className="space-y-4">
      <div className="mb-3">
        <h3 className="text-xl font-bold text-green-800">{strategy.strategyName}</h3>
        <p className="text-sm text-gray-600 mt-2">
          {strategy.overview.split('.')[0]}.
        </p>
      </div>

      {/* Main strategy details in a more spacious layout */}
      <div className="grid grid-cols-1 gap-6 mb-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center mb-3">
            <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h4 className="font-medium text-gray-700 text-lg">Strategy Details</h4>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="bg-gray-50 p-3 rounded border border-gray-100">
                <div className="text-gray-500 text-sm mb-1">Entry Time</div>
                <div className="font-medium">{strategy.entryTime || 'Not specified'}</div>
              </div>

              <div className="bg-gray-50 p-3 rounded border border-gray-100">
                <div className="text-gray-500 text-sm mb-1">Expiration</div>
                <div className="font-medium">{strategy.expirationTimeframe || 'Not specified'}</div>
              </div>
            </div>

            <div className="space-y-3">
              <div className="bg-gray-50 p-3 rounded border border-gray-100">
                <div className="text-gray-500 text-sm mb-1">Strike Selection</div>
                <div className="font-medium">{strategy.strikeSelection || 'Not specified'}</div>
              </div>

              <div className="bg-gray-50 p-3 rounded border border-gray-100">
                <div className="text-gray-500 text-sm mb-1">Implied Volatility</div>
                <div className="font-medium">{strategy.impliedVolatility || 'Not specified'}</div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center mb-3">
            <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <h4 className="font-medium text-gray-700 text-lg">Risk/Reward Profile</h4>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="bg-gray-50 p-3 rounded border border-gray-100">
                <div className="text-gray-500 text-sm mb-1">Position Size</div>
                <div className="font-medium">{strategy.positionSize || 'Not specified'}</div>
              </div>

              <div className="bg-gray-50 p-3 rounded border border-gray-100">
                <div className="text-gray-500 text-sm mb-1">Risk/Reward Ratio</div>
                <div className="font-medium">{strategy.riskReward || 'Not specified'}</div>
              </div>
            </div>

            <div className="space-y-3">
              <div className="bg-gray-50 p-3 rounded border border-gray-100">
                <div className="text-gray-500 text-sm mb-1">Profit Potential</div>
                <div className="font-medium text-green-600">{strategy.profitPotential || 'Not specified'}</div>
              </div>

              <div className="bg-gray-50 p-3 rounded border border-gray-100">
                <div className="text-gray-500 text-sm mb-1">Maximum Loss</div>
                <div className="font-medium text-red-600">{strategy.maxLoss || 'Not specified'}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Market Environment Section */}
      <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
        <div className="flex items-center mb-3">
          <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
          </svg>
          <h4 className="font-medium text-gray-700 text-lg">Market Environment</h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gray-50 p-3 rounded border border-gray-100">
            <div className="text-gray-500 text-sm mb-1">Current Trend</div>
            <div className="font-medium">{strategy.currentTrend || 'Not specified'}</div>
          </div>

          <div className="bg-gray-50 p-3 rounded border border-gray-100">
            <div className="text-gray-500 text-sm mb-1">Volatility</div>
            <div className="font-medium">{strategy.volatilityEnvironment || 'Not specified'}</div>
          </div>

          <div className="bg-gray-50 p-3 rounded border border-gray-100">
            <div className="text-gray-500 text-sm mb-1">Market Sentiment</div>
            <div className="font-medium">{strategy.sentiment || 'Not specified'}</div>
          </div>
        </div>
      </div>

      {/* Risk Management Section */}
      <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
        <div className="flex items-center mb-3">
          <svg className="w-5 h-5 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <h4 className="font-medium text-gray-700 text-lg">Risk Management</h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gray-50 p-3 rounded border border-gray-100">
            <div className="text-gray-500 text-sm mb-1">Stop Loss</div>
            <div className="font-medium">{strategy.stopLoss || 'Not specified'}</div>
          </div>

          <div className="bg-gray-50 p-3 rounded border border-gray-100">
            <div className="text-gray-500 text-sm mb-1">Take Profit</div>
            <div className="font-medium">{strategy.takeProfit || 'Not specified'}</div>
          </div>

          <div className="bg-gray-50 p-3 rounded border border-gray-100">
            <div className="text-gray-500 text-sm mb-1">Adjustment Criteria</div>
            <div className="font-medium">{strategy.adjustmentCriteria || 'Not specified'}</div>
          </div>
        </div>
      </div>

      {/* Related Stocks Section */}
      {strategy.relatedStocks && strategy.relatedStocks.length > 0 && (
        <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center mb-3">
            <svg className="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
            </svg>
            <h4 className="font-medium text-gray-700 text-lg">Related Stocks</h4>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {strategy.relatedStocks.map((stock: any, index: number) => (
              <div key={index} className="bg-gray-50 p-3 rounded border border-gray-100">
                <div className="font-medium text-lg text-gray-800 mb-2">{stock.symbol}</div>
                <div className="flex items-center mb-2">
                  <span className="text-gray-500 text-sm mr-2">Recommendation:</span>
                  <span className={`font-medium px-2 py-1 rounded-full text-xs ${
                    stock.recommendation.toLowerCase() === 'buy' ? 'bg-green-100 text-green-800' :
                    stock.recommendation.toLowerCase() === 'sell' ? 'bg-red-100 text-red-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                    {stock.recommendation}
                  </span>
                </div>
                {stock.rationale && (
                  <div className="text-sm text-gray-600 mt-1">{stock.rationale}</div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Full Strategy Overview */}
      <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
        <div className="flex items-center mb-3">
          <svg className="w-5 h-5 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h4 className="font-medium text-gray-700 text-lg">Strategy Overview</h4>
        </div>
        <div className="bg-gray-50 p-4 rounded border border-gray-100">
          <p className="text-gray-600 whitespace-pre-wrap">{strategy.overview || 'No overview available.'}</p>
        </div>
      </div>
    </div>
  );
};

export default ParsedOptionsStrategy;
