import React, { useState, useCallback, useEffect } from 'react';
import MarkdownLinkDetector from './MarkdownLinkDetector';

interface NewsItemProps {
  title: string;
  content: string;
  source?: string;
  timestamp?: string;
  impact?: 'high' | 'medium' | 'low';
  url?: string;
}

const NewsItem: React.FC<NewsItemProps> = ({ title, content, source, timestamp, impact = 'medium', url }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [validatedUrl, setValidatedUrl] = useState<string | undefined>(undefined);

  const impactColor = {
    high: 'bg-red-100 border-red-400 text-red-800',
    medium: 'bg-yellow-100 border-yellow-400 text-yellow-800',
    low: 'bg-green-100 border-green-400 text-green-800',
  };

  const impactBadgeColor = {
    high: 'bg-red-500 text-white',
    medium: 'bg-yellow-500 text-white',
    low: 'bg-green-500 text-white',
  };

  // Ensure the URL is properly formatted
  const getValidUrl = useCallback((rawUrl?: string) => {
    if (!rawUrl) {
      console.log("No URL provided for article:", title);
      return undefined;
    }

    // Log the raw URL for debugging
    console.log(`Processing URL for "${title}": ${rawUrl}`);

    // Special case for empty or placeholder URLs
    if (rawUrl === '#' || rawUrl === 'about:blank') {
      console.log(`Skipping placeholder URL: ${rawUrl}`);
      return undefined;
    }

    // Remove markdown formatting
    if (rawUrl.includes('[') && rawUrl.includes('](')) {
      // Handle markdown links: [text](url)
      const markdownMatch = rawUrl.match(/\[([^\]]+)\]\(([^)]+)\)/);
      if (markdownMatch) {
        rawUrl = markdownMatch[2];
        console.log(`Fixed markdown link: ${rawUrl}`);
      }
    } else if (rawUrl.includes('[') && rawUrl.includes(']')) {
      // Handle bracketed URLs: [https://example.com]
      const bracketMatch = rawUrl.match(/\[(https?:\/\/[^\]]+)\]/);
      if (bracketMatch) {
        rawUrl = bracketMatch[1];
        console.log(`Fixed URL with square brackets: ${rawUrl}`);
      }
    }

    // Fix double URLs (another common issue from the API)
    if (rawUrl.includes('https://https://')) {
      rawUrl = rawUrl.replace('https://https://', 'https://');
      console.log(`Fixed double protocol: ${rawUrl}`);
    }

    // Fix URLs that contain another URL in them
    const embeddedUrlMatch = rawUrl.match(/https?:\/\/[^\/]+(https?:\/\/.*)/);
    if (embeddedUrlMatch) {
      rawUrl = embeddedUrlMatch[1];
      console.log(`Extracted embedded URL: ${rawUrl}`);
    }

    // Remove any trailing punctuation
    rawUrl = rawUrl.replace(/[.,;:"'\)\]]+$/, '');

    // Fix HTML entities
    rawUrl = rawUrl.replace(/&amp;/g, '&');

    let validUrl: string | undefined = undefined;

    try {
      // Try to create a URL object to validate it
      const urlObj = new URL(rawUrl);
      validUrl = urlObj.toString();
      console.log(`Valid URL: ${validUrl}`);
    } catch (e) {
      console.log(`URL validation failed for: ${rawUrl}. Attempting to fix...`);

      // If it fails, try to add https:// prefix if missing
      if (!rawUrl.startsWith('http://') && !rawUrl.startsWith('https://')) {
        try {
          const fixedUrl = 'https://' + rawUrl.replace(/^\/\//, '');
          const urlWithProtocol = new URL(fixedUrl);
          validUrl = urlWithProtocol.toString();
          console.log(`Fixed URL by adding protocol: ${validUrl}`);
        } catch (e) {
          console.error(`Failed to fix URL: ${rawUrl}`, e);
          validUrl = undefined;
        }
      } else {
        // Try encoding the URL
        try {
          const encodedUrl = encodeURI(rawUrl);
          const urlObj = new URL(encodedUrl);
          validUrl = urlObj.toString();
          console.log(`Fixed URL by encoding: ${validUrl}`);
        } catch (e) {
          console.error(`Invalid URL that already has protocol: ${rawUrl}`, e);
          validUrl = undefined;
        }
      }
    }

    return validUrl;
  }, [title]);

  // Process URL on component mount or when URL changes
  useEffect(() => {
    const processedUrl = getValidUrl(url);
    setValidatedUrl(processedUrl);

    // Log whether we have a valid URL for debugging
    if (processedUrl) {
      console.log(`✅ Valid URL for "${title}": ${processedUrl}`);
    } else {
      console.log(`❌ No valid URL for "${title}"`);
    }
  }, [url, getValidUrl, title]);

  const handleClick = useCallback((e: React.MouseEvent) => {
    // Only process if not clicking on a link or button (to avoid double navigation)
    if ((e.target as Element).closest('a, button')) {
      return;
    }

    // Only navigate if we have a valid URL
    if (validatedUrl) {
      console.log("Opening URL:", validatedUrl);

      try {
        window.open(validatedUrl, '_blank');
      } catch (error) {
        console.error("Failed to open URL with window.open, trying alternative:", error);
        // Fallback if window.open is blocked
        const newTab = document.createElement('a');
        newTab.href = validatedUrl;
        newTab.target = '_blank';
        newTab.rel = 'noopener noreferrer';
        document.body.appendChild(newTab);
        newTab.click();
        document.body.removeChild(newTab);
      }
    } else {
      console.log("No valid URL to open for:", title);
    }
  }, [validatedUrl, title]);

  // Link click handler to prevent click event from bubbling up to parent div
  const handleLinkClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
  }, []);

  // Parse domain from URL for display
  const getDomain = (url: string) => {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace('www.', '');
    } catch (e) {
      return url;
    }
  };

  return (
    <div
      className={`mb-6 border rounded-lg shadow-sm transition-all duration-300 overflow-hidden
                 ${validatedUrl ? 'hover:shadow-lg cursor-pointer group' : ''}
                 bg-white hover:bg-blue-50/50`}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      role={validatedUrl ? "link" : "article"}
      aria-label={validatedUrl ? `Read article: ${title}` : undefined}
      tabIndex={validatedUrl ? 0 : undefined}
      style={validatedUrl ? { cursor: 'pointer' } : undefined}
      data-url={validatedUrl} // Add data attribute for easier debugging
    >
      <div className="relative">
        {/* Impact badge - positioned at the top right */}
        {impact && (
          <div className="absolute top-4 right-4 z-10">
            <span className={`text-xs font-semibold px-3 py-1 rounded-full shadow-sm ${impactBadgeColor[impact]}`}>
              {impact.toUpperCase()} IMPACT
            </span>
          </div>
        )}

        {/* Content section with padding */}
        <div className="p-5">
          <h3 className="text-xl font-bold text-gray-800 group-hover:text-blue-700 flex items-start mb-3">
            <span>{title}</span>
            {validatedUrl && (
              <a
                href={validatedUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="ml-2 text-blue-500 hover:text-blue-700 mt-1"
                title="Open source link"
                onClick={handleLinkClick}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </a>
            )}
          </h3>

          <div className="text-gray-600 mb-4 whitespace-pre-line leading-relaxed">
            <MarkdownLinkDetector
              content={content}
              onLinkClick={(url) => {
                if (url) {
                  window.open(url, '_blank');
                }
              }}
            />
          </div>

          {/* Source and metadata footer */}
          <div className="flex items-center justify-between text-xs text-gray-500 mt-4 pt-3 border-t border-gray-100">
            <div className="flex items-center space-x-2">
              {source && (
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1M19 20a2 2 0 002-2V8a2 2 0 00-2-2h-1M8 7v8m4-8v8M8 11h4" />
                  </svg>
                  <span className="font-medium">{source}</span>
                </div>
              )}

              {timestamp && (
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>{timestamp}</span>
                </div>
              )}
            </div>

            {validatedUrl && (
              <a
                href={validatedUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-blue-500 hover:text-blue-700 hover:underline group-hover:text-blue-600 font-medium"
                onClick={handleLinkClick}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 010-5.656l4-4a4 4 0 015.656 5.656l-1.1 1.1" />
                </svg>
                {getDomain(validatedUrl)}
              </a>
            )}
          </div>
        </div>

        {/* Bottom accent bar with color based on impact */}
        <div className={`h-1 w-full ${
          impact === 'high' ? 'bg-red-500' :
          impact === 'medium' ? 'bg-yellow-500' :
          'bg-green-500'
        } transition-all duration-300 group-hover:h-2`}></div>

        {/* "Open Article" floating button on hover */}
        {validatedUrl && isHovered && (
          <div className="absolute bottom-0 right-0 mb-4 mr-4 z-10">
            <div className="bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg flex items-center transform transition-transform duration-300 hover:scale-105">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
              Open Article
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default NewsItem;