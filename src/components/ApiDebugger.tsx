import React, { useState, useEffect } from 'react';
import finnhubService from '../services/finnhubService';

interface ApiDebuggerProps {
  isVisible: boolean;
  apiKey?: string;
}

const ApiDebugger: React.FC<ApiDebuggerProps> = ({ isVisible, apiKey }) => {
  const [status, setStatus] = useState<{ connected: boolean; message: string } | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastCheck, setLastCheck] = useState<string>('');
  const [showFullKey, setShowFullKey] = useState(false);

  const checkApiStatus = async () => {
    setIsLoading(true);
    try {
      // For WebSocket, we can only check if the key is set
      // The actual connection status will be tracked separately
      const isKeySet = !!apiKey && apiKey.length > 0;
      
      setStatus({
        connected: isKeySet,
        message: isKeySet 
          ? "Finnhub WebSocket API key is set. Connection status shown in Market Data section."
          : "Finnhub API key is missing. Add VITE_FINNHUB_API_KEY to your .env file."
      });
      
      setLastCheck(new Date().toLocaleTimeString());
    } catch (error) {
      setStatus({
        connected: false,
        message: `Error checking API: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isVisible) {
      checkApiStatus();
    }
  }, [isVisible]);

  if (!isVisible) return null;

  // Mask the API key for security
  const maskedKey = apiKey 
    ? `${apiKey.substring(0, 4)}...${apiKey.substring(apiKey.length - 4)}`
    : 'Not set';

  return (
    <div className="mt-6 p-4 bg-gray-100 rounded-lg border border-gray-300">
      <h3 className="text-lg font-bold text-gray-700 mb-2 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
        </svg>
        Finnhub WebSocket API Debugger
      </h3>
      
      <div className="space-y-3 text-sm">
        <div className="flex items-center justify-between bg-white p-2 rounded border border-gray-200">
          <span className="font-medium text-gray-700">API Key:</span>
          <div className="flex items-center">
            <code className="bg-gray-100 px-2 py-1 rounded text-blue-700">
              {showFullKey ? apiKey : maskedKey}
            </code>
            <button 
              onClick={() => setShowFullKey(!showFullKey)}
              className="ml-2 text-xs px-2 py-1 bg-gray-200 hover:bg-gray-300 rounded"
            >
              {showFullKey ? 'Hide' : 'Show'}
            </button>
          </div>
        </div>
        
        <div className="flex items-center justify-between bg-white p-2 rounded border border-gray-200">
          <span className="font-medium text-gray-700">Status:</span>
          <span className={`px-2 py-1 rounded-full text-white ${status?.connected ? 'bg-green-500' : 'bg-red-500'}`}>
            {status?.connected ? 'Available' : 'Missing'}
          </span>
        </div>
        
        <div className="bg-white p-2 rounded border border-gray-200">
          <span className="font-medium text-gray-700">Message:</span>
          <p className="mt-1 text-gray-600">{status?.message || 'Checking...'}</p>
        </div>
        
        <div className="flex items-center justify-between bg-white p-2 rounded border border-gray-200">
          <span className="font-medium text-gray-700">Last Checked:</span>
          <span className="text-gray-600">{lastCheck || 'Never'}</span>
        </div>
        
        <div className="flex items-center justify-between pt-2">
          <button
            onClick={checkApiStatus}
            disabled={isLoading}
            className={`px-3 py-1.5 rounded-lg text-white ${
              isLoading ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'
            } transition-colors flex items-center`}
          >
            {isLoading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Checking...
              </>
            ) : (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Check Again
              </>
            )}
          </button>
          
          <div className="text-xs text-gray-500">
            <span>WebSocket: No rate limits</span>
          </div>
        </div>
      </div>
      
      <div className="mt-4 text-xs text-gray-500">
        <p>Note: Finnhub WebSocket has no rate limits and provides real-time data. Connection status shown in Market Overview.</p>
      </div>
    </div>
  );
};

export default ApiDebugger; 