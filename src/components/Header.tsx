import React from 'react';

const Header: React.FC = () => {
  return (
    <header className="bg-gradient-to-r from-blue-800 to-blue-600 text-white shadow-md">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-8 w-8 animate-pulse"
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="2.5" 
            strokeLinecap="round" 
            strokeLinejoin="round"
          >
            <path d="M6 9l6 6 6-6" />
            <path d="M6 16l6 6 6-6" />
            <path d="M6 2l6 6 6-6" />
          </svg>
          <div>
            <h1 className="text-xl font-bold tracking-tight transition-all duration-300 hover:text-blue-200 cursor-default">
              SPY News Aggregator
            </h1>
            <p className="text-blue-200 text-sm">Powered by Sonar Deep Research</p>
          </div>
        </div>
        
        <div className="hidden md:flex items-center space-x-2">
          <span className="bg-blue-800 hover:bg-blue-900 text-sm px-3 py-1 rounded-full transition-all duration-300 transform hover:scale-105">
            Trading Day: {new Date().toLocaleDateString()}
          </span>
        </div>
      </div>
    </header>
  );
};

export default Header; 