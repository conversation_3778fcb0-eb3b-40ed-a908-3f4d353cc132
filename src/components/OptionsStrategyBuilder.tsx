import React, { useState, useEffect, useMemo } from 'react';
import { OptionLeg } from './OptionsStrategyChart';
import OptionsStrategyChart from './OptionsStrategyChart';

interface OptionsStrategyBuilderProps {
  suggestedStrategy: string;
  suggestedStrikes: string;
  initialData: {
    payoffData: Array<{ price: number; profit: number }>;
    breakEvenPoints: number[];
    maxProfit?: number;
    maxLoss?: number;
  };
}

// Calculate profit at expiration for a given underlying price
function calculateProfit(legs: OptionLeg[], underlyingPrice: number): number {
  let totalProfit = 0;
  
  legs.forEach(leg => {
    const { action, type, strike, premium, quantity } = leg;
    let legProfit = 0;
    
    if (type === 'call') {
      // Call option payoff: max(0, underlying price - strike)
      const intrinsicValue = Math.max(0, underlyingPrice - strike);
      
      if (action === 'buy') {
        // Long call: intrinsicValue - premium
        legProfit = (intrinsicValue - premium) * quantity * 100; // Each contract is for 100 shares
      } else {
        // Short call: premium - intrinsicValue
        legProfit = (premium - intrinsicValue) * quantity * 100;
      }
    } else {
      // Put option payoff: max(0, strike - underlying price)
      const intrinsicValue = Math.max(0, strike - underlyingPrice);
      
      if (action === 'buy') {
        // Long put: intrinsicValue - premium
        legProfit = (intrinsicValue - premium) * quantity * 100;
      } else {
        // Short put: premium - intrinsicValue
        legProfit = (premium - intrinsicValue) * quantity * 100;
      }
    }
    
    totalProfit += legProfit;
  });
  
  return totalProfit;
}

// Generate payoff data for a specific strategy with given legs
function generatePayoffData(
  legs: OptionLeg[], 
  minPrice: number, 
  maxPrice: number, 
  steps: number = 40
): {
  payoffData: Array<{ price: number; profit: number }>;
  maxProfit: number;
  maxLoss: number;
  breakEvenPoints: number[];
} {
  const payoffData: Array<{ price: number; profit: number }> = [];
  const step = (maxPrice - minPrice) / steps;
  let maxProfit = -Infinity;
  let minProfit = Infinity;
  
  // Calculate profit at each price point
  for (let price = minPrice; price <= maxPrice; price += step) {
    const profit = calculateProfit(legs, price);
    payoffData.push({ price, profit });
    
    maxProfit = Math.max(maxProfit, profit);
    minProfit = Math.min(minProfit, profit);
  }
  
  // Find break-even points (where profit crosses zero)
  const breakEvenPoints: number[] = [];
  for (let i = 1; i < payoffData.length; i++) {
    const prev = payoffData[i-1];
    const curr = payoffData[i];
    
    // If profit crosses zero between these points
    if ((prev.profit < 0 && curr.profit > 0) || (prev.profit > 0 && curr.profit < 0)) {
      // Interpolate to find the break-even point
      const ratio = Math.abs(prev.profit) / (Math.abs(prev.profit) + Math.abs(curr.profit));
      const breakEvenPrice = prev.price + ratio * (curr.price - prev.price);
      breakEvenPoints.push(Number(breakEvenPrice.toFixed(2)));
    }
  }
  
  return {
    payoffData,
    maxProfit: Math.max(0, maxProfit),
    maxLoss: Math.abs(Math.min(0, minProfit)),
    breakEvenPoints
  };
}

const OptionsStrategyBuilder: React.FC<OptionsStrategyBuilderProps> = ({ 
  suggestedStrategy,
  suggestedStrikes,
  initialData
}) => {
  // Parse suggested strategy and strikes to create initial legs
  const initialLegs: OptionLeg[] = useMemo(() => {
    // Default to a simple bull call spread if we can't parse
    const defaultLegs: OptionLeg[] = [
      { action: 'buy', type: 'call', strike: 450, premium: 5, quantity: 1 },
      { action: 'sell', type: 'call', strike: 460, premium: 2, quantity: 1 }
    ];
    
    try {
      // Try to extract strikes from the suggested strikes string
      const strikeMatch = suggestedStrikes.match(/(\d+)/g);
      const strikes = strikeMatch ? strikeMatch.map(Number) : [450, 460];
      
      if (suggestedStrategy.toLowerCase().includes('bull call spread')) {
        return [
          { action: 'buy', type: 'call', strike: strikes[0] || 450, premium: 5, quantity: 1 },
          { action: 'sell', type: 'call', strike: strikes[1] || 460, premium: 2, quantity: 1 }
        ];
      } else if (suggestedStrategy.toLowerCase().includes('bear put spread')) {
        return [
          { action: 'buy', type: 'put', strike: strikes[1] || 460, premium: 5, quantity: 1 },
          { action: 'sell', type: 'put', strike: strikes[0] || 450, premium: 2, quantity: 1 }
        ];
      } else if (suggestedStrategy.toLowerCase().includes('iron condor')) {
        return [
          { action: 'sell', type: 'put', strike: strikes[0] - 10 || 440, premium: 2, quantity: 1 },
          { action: 'buy', type: 'put', strike: strikes[0] || 450, premium: 3, quantity: 1 },
          { action: 'sell', type: 'call', strike: strikes[1] || 460, premium: 2, quantity: 1 },
          { action: 'buy', type: 'call', strike: strikes[1] + 10 || 470, premium: 1, quantity: 1 }
        ];
      }
      
      return defaultLegs;
    } catch (e) {
      console.error("Error parsing strategy:", e);
      return defaultLegs;
    }
  }, [suggestedStrategy, suggestedStrikes]);
  
  // State for custom legs
  const [legs, setLegs] = useState<OptionLeg[]>(initialLegs);
  
  // State for whether to use custom builder
  const [useCustomBuilder, setUseCustomBuilder] = useState(false);
  
  // Generate payoff data when legs change
  const customData = useMemo(() => {
    if (legs.length === 0) return initialData;
    
    // Find min/max price range based on strike prices
    const strikes = legs.map(leg => leg.strike);
    const minStrike = Math.min(...strikes);
    const maxStrike = Math.max(...strikes);
    const range = Math.max(50, maxStrike - minStrike); // At least 50 points range
    
    const minPrice = Math.max(1, minStrike - range * 0.3); // Ensure positive price
    const maxPrice = maxStrike + range * 0.3;
    
    return generatePayoffData(legs, minPrice, maxPrice);
  }, [legs, initialData]);
  
  // Update a leg
  const updateLeg = (index: number, updates: Partial<OptionLeg>) => {
    const newLegs = [...legs];
    newLegs[index] = { ...newLegs[index], ...updates };
    setLegs(newLegs);
  };
  
  // Add a new leg
  const addLeg = () => {
    if (legs.length >= 4) return; // Limit to 4 legs for simplicity
    
    // Create a new leg based on the last one
    const lastLeg = legs[legs.length - 1];
    const newLeg = { 
      ...lastLeg, 
      action: lastLeg.action === 'buy' ? 'sell' : 'buy',
      strike: lastLeg.strike + 10
    };
    setLegs([...legs, newLeg]);
  };
  
  // Remove a leg
  const removeLeg = (index: number) => {
    if (legs.length <= 1) return; // Must have at least one leg
    
    const newLegs = legs.filter((_, i) => i !== index);
    setLegs(newLegs);
  };

  return (
    <div className="mt-6">
      <div className="flex items-center mb-4">
        <h3 className="text-lg font-medium text-gray-800">Strategy Payoff Analysis</h3>
        <div className="ml-auto">
          <button
            onClick={() => setUseCustomBuilder(!useCustomBuilder)}
            className={`px-3 py-1 text-sm rounded ${
              useCustomBuilder 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            {useCustomBuilder ? 'Using Custom Builder' : 'Use Strategy Builder'}
          </button>
        </div>
      </div>
      
      {useCustomBuilder && (
        <div className="mb-6 bg-gray-50 p-4 rounded-lg border border-gray-200">
          <h4 className="font-medium text-gray-800 mb-3">Custom Strategy Options</h4>
          
          <div className="space-y-4">
            {legs.map((leg, index) => (
              <div key={index} className="flex flex-wrap items-center bg-white p-3 rounded border border-gray-300 gap-x-4 gap-y-2">
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Action</label>
                  <select
                    value={leg.action}
                    onChange={(e) => updateLeg(index, { action: e.target.value as 'buy' | 'sell' })}
                    className="px-2 py-1 border border-gray-300 rounded w-24"
                  >
                    <option value="buy">Buy</option>
                    <option value="sell">Sell</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Type</label>
                  <select
                    value={leg.type}
                    onChange={(e) => updateLeg(index, { type: e.target.value as 'call' | 'put' })}
                    className="px-2 py-1 border border-gray-300 rounded w-24"
                  >
                    <option value="call">Call</option>
                    <option value="put">Put</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Strike ($)</label>
                  <input
                    type="number"
                    value={leg.strike}
                    onChange={(e) => updateLeg(index, { strike: Number(e.target.value) })}
                    className="px-2 py-1 border border-gray-300 rounded w-24"
                    min="1"
                    step="1"
                  />
                </div>
                
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Premium ($)</label>
                  <input
                    type="number"
                    value={leg.premium}
                    onChange={(e) => updateLeg(index, { premium: Number(e.target.value) })}
                    className="px-2 py-1 border border-gray-300 rounded w-24"
                    min="0.01"
                    step="0.01"
                  />
                </div>
                
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Quantity</label>
                  <input
                    type="number"
                    value={leg.quantity}
                    onChange={(e) => updateLeg(index, { quantity: Math.max(1, Number(e.target.value)) })}
                    className="px-2 py-1 border border-gray-300 rounded w-24"
                    min="1"
                    step="1"
                  />
                </div>
                
                <div className="ml-auto flex items-center">
                  <button
                    onClick={() => removeLeg(index)}
                    disabled={legs.length <= 1}
                    className={`text-xs px-2 py-1 rounded ${
                      legs.length <= 1
                        ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                        : 'bg-red-100 text-red-700 hover:bg-red-200'
                    }`}
                  >
                    Remove
                  </button>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-4">
            <button
              onClick={addLeg}
              disabled={legs.length >= 4}
              className={`text-sm px-3 py-1 rounded ${
                legs.length >= 4
                  ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              Add Leg
            </button>
            <span className="ml-2 text-xs text-gray-500">
              {legs.length >= 4 ? 'Maximum 4 legs allowed' : 'Add up to 4 legs'}
            </span>
          </div>
          
          <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-green-50 p-3 rounded border border-green-200">
              <div className="text-sm font-medium text-green-800">Max Profit</div>
              <div className="text-lg font-bold text-green-600">${customData.maxProfit.toFixed(2)}</div>
              <div className="text-xs text-green-600 mt-1">Per contract (100 shares)</div>
            </div>
            
            <div className="bg-red-50 p-3 rounded border border-red-200">
              <div className="text-sm font-medium text-red-800">Max Loss</div>
              <div className="text-lg font-bold text-red-600">${customData.maxLoss.toFixed(2)}</div>
              <div className="text-xs text-red-600 mt-1">Per contract (100 shares)</div>
            </div>
            
            <div className="bg-blue-50 p-3 rounded border border-blue-200">
              <div className="text-sm font-medium text-blue-800">Break-even Point(s)</div>
              <div className="text-lg font-bold text-blue-600">
                {customData.breakEvenPoints.length > 0 
                  ? customData.breakEvenPoints.map(point => `$${Number(point).toFixed(2)}`).join(', ')
                  : 'None'
                }
              </div>
            </div>
          </div>
          
          <div className="mt-4 text-sm text-gray-600">
            <p>
              <span className="font-medium">Strategy Type:</span> {
                legs.every(leg => leg.type === 'call') 
                  ? legs.some(leg => leg.action === 'buy') && legs.some(leg => leg.action === 'sell')
                    ? 'Call Spread'
                    : legs.every(leg => leg.action === 'buy')
                      ? 'Long Call'
                      : 'Short Call'
                  : legs.every(leg => leg.type === 'put')
                    ? legs.some(leg => leg.action === 'buy') && legs.some(leg => leg.action === 'sell')
                      ? 'Put Spread'
                      : legs.every(leg => leg.action === 'buy')
                        ? 'Long Put'
                        : 'Short Put'
                    : 'Multi-leg Strategy'
              }
            </p>
            <p className="mt-1">
              <span className="font-medium">Net Debit/Credit:</span> {
                legs.reduce((total, leg) => {
                  if (leg.action === 'buy') {
                    return total - leg.premium * leg.quantity * 100;
                  } else {
                    return total + leg.premium * leg.quantity * 100;
                  }
                }, 0) >= 0
                  ? `$${legs.reduce((total, leg) => {
                      if (leg.action === 'buy') {
                        return total - leg.premium * leg.quantity * 100;
                      } else {
                        return total + leg.premium * leg.quantity * 100;
                      }
                    }, 0).toFixed(2)} Credit`
                  : `$${Math.abs(legs.reduce((total, leg) => {
                      if (leg.action === 'buy') {
                        return total - leg.premium * leg.quantity * 100;
                      } else {
                        return total + leg.premium * leg.quantity * 100;
                      }
                    }, 0)).toFixed(2)} Debit`
              }
            </p>
          </div>
        </div>
      )}
      
      <OptionsStrategyChart
        strategyType={
          useCustomBuilder
            ? legs.every(leg => leg.type === 'call')
              ? 'bullish'
              : legs.every(leg => leg.type === 'put')
                ? 'bearish'
                : 'neutral'
            : suggestedStrategy
        }
        payoffData={useCustomBuilder ? customData.payoffData : initialData.payoffData}
        breakEvenPoints={useCustomBuilder ? customData.breakEvenPoints : initialData.breakEvenPoints}
        maxProfit={useCustomBuilder ? customData.maxProfit : initialData.maxProfit}
        maxLoss={useCustomBuilder ? customData.maxLoss : initialData.maxLoss}
      />
      
      {/* Exit Strategy Section */}
      <div className="mt-6 bg-red-50 p-4 rounded-lg border border-red-200">
        <h3 className="text-lg font-medium text-red-800 mb-2">Position Exit Strategy</h3>
        
        <div className="space-y-3">
          <div>
            <h4 className="font-medium text-red-700">When to Close Position:</h4>
            <ul className="mt-1 list-disc pl-5 text-sm text-red-700">
              <li>Take profit at 50-75% of maximum potential profit</li>
              <li>Exit if loss reaches 50% of maximum potential loss</li>
              <li>Close 7-10 days before expiration to avoid accelerated theta decay</li>
              <li>Exit if the original thesis for the trade is invalidated by market conditions</li>
              <li>Consider rolling to a later expiration if still bullish/bearish but need more time</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium text-red-700">Risk Management:</h4>
            <ul className="mt-1 list-disc pl-5 text-sm text-red-700">
              <li>Set stop-loss orders or alerts at critical price levels</li>
              <li>Monitor implied volatility - higher IV increases premium values</li>
              <li>Track greeks, especially theta (time decay) and delta (price sensitivity)</li>
              <li>Consider closing partial positions when profitable</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OptionsStrategyBuilder;