import React from 'react';
import { MarketData } from '../services/marketData';

interface MarketDataDebuggerProps {
  isVisible: boolean;
  marketData: MarketData | null;
  isLoading: boolean;
  isError: boolean;
  lastRefreshTime: string;
  onRefresh: () => void;
}

const MarketDataDebugger: React.FC<MarketDataDebuggerProps> = ({
  isVisible,
  marketData,
  isLoading,
  isError,
  lastRefreshTime,
  onRefresh
}) => {
  if (!isVisible) return null;

  return (
    <div className="mt-6 p-4 bg-gray-100 rounded-lg border border-gray-300">
      <h3 className="text-lg font-bold text-gray-700 mb-2 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
        </svg>
        Market Data Debugger
      </h3>
      
      <div className="space-y-3 text-sm">
        <div className="flex items-center justify-between bg-white p-2 rounded border border-gray-200">
          <span className="font-medium text-gray-700">Status:</span>
          <div className="flex items-center space-x-2">
            {isLoading && (
              <span className="px-2 py-1 rounded-full bg-blue-500 text-white flex items-center">
                <svg className="animate-spin -ml-0.5 mr-1.5 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Loading
              </span>
            )}
            
            {!isLoading && isError && (
              <span className="px-2 py-1 rounded-full bg-red-500 text-white">Error</span>
            )}
            
            {!isLoading && !isError && (
              <span className="px-2 py-1 rounded-full bg-green-500 text-white">Connected</span>
            )}
          </div>
        </div>
        
        <div className="flex items-center justify-between bg-white p-2 rounded border border-gray-200">
          <span className="font-medium text-gray-700">Last Update:</span>
          <span className="text-gray-600">{marketData?.lastUpdate || 'Not yet updated'}</span>
        </div>
        
        <div className="bg-white p-2 rounded border border-gray-200">
          <div className="flex justify-between items-center mb-2">
            <span className="font-medium text-gray-700">Data:</span>
            <button 
              onClick={onRefresh} 
              disabled={isLoading}
              className="text-xs px-2 py-1 bg-blue-100 text-blue-700 hover:bg-blue-200 rounded flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Reconnect WebSocket
            </button>
          </div>
          
          {isError ? (
            <div className="p-2 bg-red-50 text-red-600 rounded">
              WebSocket connection error. Please check your API key and try reconnecting.
            </div>
          ) : isLoading ? (
            <div className="p-2 bg-gray-50 text-gray-500 rounded animate-pulse">
              Connecting to Finnhub WebSocket...
            </div>
          ) : marketData ? (
            <pre className="p-2 bg-gray-50 text-xs overflow-auto rounded max-h-48">
              {JSON.stringify(marketData, null, 2)}
            </pre>
          ) : (
            <div className="p-2 bg-yellow-50 text-yellow-600 rounded">
              No market data received yet. Waiting for WebSocket updates.
            </div>
          )}
        </div>
        
        <div className="bg-white p-2 rounded border border-gray-200">
          <span className="font-medium text-gray-700">Connection Info:</span>
          <div className="grid grid-cols-2 gap-2 mt-2 text-xs">
            <div className="p-1 bg-gray-50 rounded">
              <span className="font-medium">WebSocket Endpoint:</span>
              <p className="text-gray-600 truncate mt-1">wss://ws.finnhub.io?token=***</p>
            </div>
            <div className="p-1 bg-gray-50 rounded">
              <span className="font-medium">Subscription:</span>
              <p className="text-gray-600 mt-1">{'{"type":"subscribe","symbol":"SPY"}'}</p>
            </div>
            <div className="p-1 bg-gray-50 rounded">
              <span className="font-medium">Symbol:</span>
              <p className="text-gray-600 mt-1">SPY</p>
            </div>
            <div className="p-1 bg-gray-50 rounded">
              <span className="font-medium">Rate Limit:</span>
              <p className="text-gray-600 mt-1">None (WebSocket)</p>
            </div>
          </div>
        </div>
      </div>
      
      <div className="mt-4 text-xs text-gray-500">
        <p>Note: Real-time data from Finnhub WebSocket. Reconnect if the connection is lost.</p>
      </div>
    </div>
  );
};

export default MarketDataDebugger; 