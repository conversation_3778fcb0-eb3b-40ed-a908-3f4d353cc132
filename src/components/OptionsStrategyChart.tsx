import React from 'react';
import {
  <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON>A<PERSON>s, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  AreaChart, Area
} from 'recharts';

// Types for option strategy payoff visualization
interface PayoffPoint {
  price: number;
  profit: number;
}

// Define types for option legs
export interface OptionLeg {
  action: 'buy' | 'sell';
  type: 'call' | 'put';
  strike: number;
  premium: number;
  quantity: number;
}

interface OptionsStrategyChartProps {
  strategyType: string;
  payoffData: PayoffPoint[];
  breakEvenPoints: number[];
  strikePrice?: number;
  maxProfit?: number;
  maxLoss?: number;
  currentPrice?: number;
}

const OptionsStrategyChart: React.FC<OptionsStrategyChartProps> = ({
  strategyType,
  payoffData,
  breakEvenPoints,
  strikePrice,
  maxProfit,
  maxLoss,
  currentPrice
}) => {
  // Determine color scheme based on strategy type
  const getColorScheme = () => {
    switch(strategyType.toLowerCase()) {
      case 'bullish':
      case 'bull call spread':
      case 'long call':
        return { primary: '#22c55e', secondary: '#bbf7d0' }; // Green
      case 'bearish':
      case 'bear put spread':
      case 'long put':
        return { primary: '#ef4444', secondary: '#fecaca' }; // Red
      case 'neutral':
      case 'iron condor':
      case 'butterfly':
        return { primary: '#6366f1', secondary: '#c7d2fe' }; // Indigo
      default:
        return { primary: '#3b82f6', secondary: '#bfdbfe' }; // Blue
    }
  };

  const colors = getColorScheme();

  // Custom tooltip to show more detailed information
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 rounded shadow-md border border-gray-200">
          <p className="font-medium text-gray-700">SPY Price: ${Number(payload[0].payload.price).toFixed(2)}</p>
          <p className={`font-medium ${payload[0].payload.profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            Profit/Loss: ${Number(payload[0].payload.profit).toFixed(2)}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="mt-4">
      <h4 className="text-lg font-medium text-gray-800 mb-2">Strategy Payoff Chart</h4>
      <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart
            data={payoffData}
            margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="price" 
              label={{ value: 'SPY Price', position: 'insideBottom', offset: -5 }}
              domain={['dataMin', 'dataMax']}
              tickFormatter={(value) => `$${Number(value).toFixed(2)}`}
            />
            <YAxis 
              label={{ value: 'Profit/Loss ($)', angle: -90, position: 'insideLeft' }} 
            />
            <Tooltip content={<CustomTooltip />} />
            <defs>
              <linearGradient id="profitGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor={colors.primary} stopOpacity={0.8}/>
                <stop offset="95%" stopColor={colors.primary} stopOpacity={0.2}/>
              </linearGradient>
            </defs>
            <Area 
              type="monotone" 
              dataKey="profit" 
              stroke={colors.primary} 
              fill="url(#profitGradient)" 
              activeDot={{ r: 6 }} 
            />
            {breakEvenPoints.map((point, idx) => (
              <Line
                key={`breakeven-${idx}`}
                type="monotone"
                dataKey="price"
                stroke="#6b7280"
                strokeDasharray="5 5"
                dot={false}
                activeDot={false}
                isAnimationActive={false}
                name={`Break Even ${idx + 1}`}
              />
            ))}
            {currentPrice && (
              <Line
                type="monotone"
                dataKey="price"
                stroke="#f59e0b"
                strokeWidth={2}
                dot={false}
                activeDot={false}
                isAnimationActive={false}
                name="Current Price"
              />
            )}
          </AreaChart>
        </ResponsiveContainer>
        
        <div className="mt-4 grid grid-cols-1 sm:grid-cols-3 gap-4">
          {maxProfit !== undefined && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3 text-center">
              <p className="text-sm text-gray-600 mb-1">Max Profit</p>
              <p className="text-xl font-bold text-green-600">
                ${maxProfit.toFixed(2)}
              </p>
            </div>
          )}
          
          {maxLoss !== undefined && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 text-center">
              <p className="text-sm text-gray-600 mb-1">Max Loss</p>
              <p className="text-xl font-bold text-red-600">
                ${Math.abs(maxLoss).toFixed(2)}
              </p>
            </div>
          )}
          
          {breakEvenPoints.length > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-center">
              <p className="text-sm text-gray-600 mb-1">Break Even</p>
              <p className="text-xl font-bold text-blue-600">
                ${breakEvenPoints.map(point => point.toFixed(2)).join(', ')}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OptionsStrategyChart;