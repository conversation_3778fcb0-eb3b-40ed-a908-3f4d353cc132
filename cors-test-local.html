<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Test with Local Server</title>
    <script>
        async function testCors() {
            const lambdaUrl = 'https://yqhtu7exlgaxtlrrrahaykfgnm0qqdvl.lambda-url.us-east-1.on.aws/';
            const resultDiv = document.getElementById('result');
            
            try {
                // First, test a simple GET request
                resultDiv.innerHTML = 'Testing GET request...';
                
                const response = await fetch(lambdaUrl, {
                    method: 'GET'
                });
                
                resultDiv.innerHTML += '<br>GET request successful!';
                resultDiv.innerHTML += '<br>Status: ' + response.status;
                
                // Now test a POST request
                resultDiv.innerHTML += '<br><br>Testing POST request...';
                
                const postResponse = await fetch(lambdaUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'test',
                        data: {
                            message: 'Hello from CORS test'
                        }
                    })
                });
                
                const data = await postResponse.json();
                resultDiv.innerHTML += '<br>POST request successful!';
                resultDiv.innerHTML += '<br>Response: ' + JSON.stringify(data);
                
            } catch (error) {
                resultDiv.innerHTML += '<br>Error: ' + error.message;
                console.error('Error:', error);
            }
        }
    </script>
</head>
<body>
    <h1>CORS Test with Local Server</h1>
    <p>This test will be run from a local HTTP server instead of a file:// URL.</p>
    <button onclick="testCors()">Test CORS</button>
    <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;"></div>
</body>
</html>