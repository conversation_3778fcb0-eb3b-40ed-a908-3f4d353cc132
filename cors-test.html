<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Test</title>
    <script>
        async function testCors() {
            const lambdaUrl = 'https://yqhtu7exlgaxtlrrrahaykfgnm0qqdvl.lambda-url.us-east-1.on.aws/';
            const resultDiv = document.getElementById('result');
            
            try {
                // First, test a preflight request
                resultDiv.innerHTML = 'Testing preflight request...';
                
                // Make a fetch request with custom headers to trigger a preflight
                const response = await fetch(lambdaUrl, {
                    method: 'OPTIONS',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'test'
                    }
                });
                
                resultDiv.innerHTML += '<br>Preflight request successful!';
                
                // Now test a regular POST request
                resultDiv.innerHTML += '<br>Testing POST request...';
                
                const postResponse = await fetch(lambdaUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'perplexityRequest',
                        requestData: {
                            model: 'sonar-pro',
                            messages: [
                                {
                                    role: 'system',
                                    content: 'You are a helpful assistant.'
                                },
                                {
                                    role: 'user',
                                    content: 'Hello'
                                }
                            ]
                        }
                    })
                });
                
                const data = await postResponse.json();
                resultDiv.innerHTML += '<br>POST request successful!';
                resultDiv.innerHTML += '<br>Response: ' + JSON.stringify(data);
                
            } catch (error) {
                resultDiv.innerHTML += '<br>Error: ' + error.message;
                console.error('Error:', error);
            }
        }
    </script>
</head>
<body>
    <h1>CORS Test</h1>
    <button onclick="testCors()">Test CORS</button>
    <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;"></div>
</body>
</html>