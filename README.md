# SPY News Aggregator with Real-time Data

A React application that aggregates news and market data about the S&P 500 ETF (SPY) using Finnhub WebSocket for real-time price updates.

## Features

- Real-time SPY price and market data via WebSocket
- News aggregation from multiple sources
- Zero rate limits for market data (WebSocket connection)
- Responsive design with Tailwind CSS

## Installation

1. Clone the repository
2. Install dependencies:
```bash
npm install
```
3. Create a `.env` file from the example:
```bash
cp .env.example .env
```
4. Get your free Finnhub API key from [Finnhub.io](https://finnhub.io/register) and add it to the `.env` file:
```
VITE_FINNHUB_API_KEY=your_finnhub_api_key_here
```
5. Start the development server:
```bash
npm run dev
```

## Using Finnhub WebSocket for Real-time Data

### Why WebSockets?

Using WebSockets for market data offers several advantages:

- **Real-time Updates**: Get price changes instantly as they happen
- **No Rate Limits**: No request limits unlike traditional REST APIs
- **Reduced Latency**: Direct connection without polling overhead
- **Lower Server Load**: Single persistent connection instead of repeated requests

### How It Works

1. **WebSocket Connection**: The app connects to Finnhub's WebSocket server when it loads
2. **Data Streaming**: Market data streams in real-time as the market moves
3. **Automatic Reconnection**: If the connection drops, the app automatically tries to reconnect
4. **Efficient Updates**: Only changed data is sent over the wire, minimizing bandwidth usage

### Getting a Finnhub API Key

1. Create a free account at [Finnhub.io](https://finnhub.io/register)
2. Navigate to your dashboard and copy your API key
3. Add the key to your `.env` file as `VITE_FINNHUB_API_KEY`

### WebSocket vs. REST API

| Feature | WebSocket | REST API |
|---------|-----------|----------|
| Update Frequency | Real-time | Rate limited |
| Connection Type | Persistent | New connection per request |
| Rate Limits | None | Usually strict limits |
| Data Delivery | Push-based | Pull-based |
| Bandwidth Usage | Efficient | Higher overhead |

## Implementation Details

The app uses a singleton pattern for the WebSocket connection to ensure only one connection is maintained. Key features include:

- Automatic reconnection with exponential backoff
- Smart handling of connection issues
- Efficient message processing
- Proper connection cleanup on component unmount

## Development

### Running Locally

After installing dependencies and setting up your API key, run:

```bash
npm run dev
```

This will start the development server, typically at http://localhost:5173 (or another port if 5173 is in use).

### Project Structure

- `src/services/finnhubService.ts` - WebSocket connection and data handling
- `src/components/MarketOverview.tsx` - Display for real-time market data
- `src/App.tsx` - Main application component connecting everything

## License

MIT
